use crate::data::DataSource;
use crate::model::account::AccountId;
use crate::model::context::Context;
use crate::model::data_source::{RestSubscribe, RestType, TimerSubscribe};
use crate::model::event::Event;
use crate::model::event::account::{AccountEvent, AccountEventInner};
use crate::model::event::market::{MarketEvent, MarketEventInner};
use crate::model::event::timer::TimerEvent;
use crate::strategy::Strategy;
use quant_api::{create_private_rest, create_private_ws};
use quant_common::base::{ExConfig, Rest, Subscribes, WebSocket, WsHandler};
use quant_common::{Result, qerror};
use tokio::spawn;
use tokio::task::JoinHandle;
use tracing::error;

pub struct StandDataSource {
    accounts: Vec<ExConfig>,
}

impl StandDataSource {
    pub fn new(accounts: Vec<ExConfig>) -> Self {
        Self { accounts }
    }
}

impl DataSource for StandDataSource {
    async fn subscribe_ws<H: WsHandler + Send + Sync + 'static>(
        &self,
        account_id: AccountId,
        subs: Subscribes<H>,
    ) -> Result<JoinHandle<()>> {
        let config = self
            .accounts
            .get(account_id)
            .ok_or(qerror!("没有找到 Account_id 对应的 ExConfig"))?;
        // 判断有无私有的频道
        let mut ws = create_private_ws(config.clone()).await?;

        Ok(spawn(async move {
            let result = ws.run(subs).await;
            if let Err(e) = result {
                error!("ws running err:{e}");
            }
        }))
    }

    /// 根据account id进行Rest轮询
    async fn subscribe_rest<H: Strategy + Send + 'static>(
        &self,
        account_id: AccountId,
        sub: RestSubscribe,
        handler: H,
    ) -> Result<JoinHandle<()>> {
        let config = self
            .accounts
            .get(account_id)
            .ok_or(qerror!("订阅Rest轮询所传递的account_id错误"))?
            .to_owned();

        let exchange = config.exchange;
        let rest = create_private_rest(config.clone()).await;

        let handle = spawn(async move {
            let mut update_interval = tokio::time::interval(sub.update_interval);
            loop {
                update_interval.tick().await;
                match sub.rest_type {
                    RestType::Balance => match rest.get_balances().await {
                        Ok(balances) => {
                            if let Err(e) = handler
                                .handle_event(Event::Account(AccountEvent {
                                    account_id,
                                    context: Context::default(),
                                    event: AccountEventInner::Balance(balances),
                                }))
                                .await
                            {
                                error!("Rest subscribe balance handle error:{e}");
                            }
                        }
                        Err(e) => {
                            error!("Rest subscribe balance error:{e}");
                        }
                    },
                    RestType::Position => match rest.get_positions().await {
                        Ok(positions) => {
                            if let Err(e) = handler
                                .handle_event(Event::Account(AccountEvent {
                                    account_id,
                                    context: Context::default(),
                                    event: AccountEventInner::Position(positions),
                                }))
                                .await
                            {
                                error!("Rest subscribe position handle error:{e}");
                            }
                        }
                        Err(e) => {
                            error!("Rest subscribe position error:{e}");
                        }
                    },
                    RestType::Funding => match rest.get_funding_rates().await {
                        Ok(fundings) => {
                            if let Err(e) = handler
                                .handle_event(Event::Market(MarketEvent {
                                    exchange,
                                    context: Context::default(),
                                    event: MarketEventInner::Funding(fundings),
                                }))
                                .await
                            {
                                error!("Rest subscribe funding handle error:{e}");
                            }
                        }
                        Err(e) => {
                            error!("Rest subscribe funding error:{e}");
                        }
                    },
                    RestType::Instrument => match rest.get_instruments().await {
                        Ok(instruments) => {
                            if let Err(e) = handler
                                .handle_event(Event::Market(MarketEvent {
                                    exchange,
                                    context: Context::default(),
                                    event: MarketEventInner::Instrument(instruments),
                                }))
                                .await
                            {
                                error!("Rest subscribe instrument handle error:{e}");
                            }
                        }
                        Err(e) => {
                            error!("Rest subscribe instrument error:{e}");
                        }
                    },
                }
            }
        });

        Ok(handle)
    }

    async fn subscribe_timer<H: Strategy + Send + 'static>(
        &self,
        subs: TimerSubscribe,
        handler: H,
    ) -> Result<JoinHandle<()>> {
        Ok(spawn(async move {
            // 首先等待初始延迟时间
            if !subs.initial_delay.is_zero() {
                tokio::time::sleep(subs.initial_delay).await;
            }

            let mut update_interval = tokio::time::interval(subs.update_interval);
            loop {
                update_interval.tick().await;
                if let Err(e) = Strategy::handle_event(
                    &handler,
                    Event::Timer(TimerEvent {
                        name: subs.name.clone(),
                    }),
                )
                .await
                {
                    error!("on_timer_subscribe error:{e}");
                }
            }
        }))
    }
}
