use std::sync::Arc;
use std::sync::atomic::{AtomicU64, Ordering};

use crate::{
    config::ExParams,
    model::{
        context::Milestone,
        event::{
            Event,
            ex_command::{AsyncCommand, Command, ExecutionAsyncCommandResult, ExecutionCommand},
        },
    },
    strategy::Strategy,
};
use once_cell::sync::OnceCell;
use quant_api::{ExchangeRest, create_private_rest, order_processor::process_order};
use quant_common::base::{
    AmendOrderResult, AsyncCmdResult, CancelOrderResult, Exchange, OrderParams, PlaceOrderResult,
    Rest, WebSocketAPI,
};
use ws_api::WsAPI;

use super::ExecutionEngine;
use quant_common::{Result, qerror};

pub mod ws_api;

/// 全局递增的请求ID计数器
///
/// u64 的最大值是 18,446,744,073,709,551,615（约1844万亿）
/// 假设不同的请求频率：
///     高频交易（每秒10,000次请求）：约58,494年后溢出
///     超高频（每秒100,000次请求）：约5,849年后溢出
///     极端情况（每秒1,000,000次请求）：约584年后溢出
static REQUEST_ID_COUNTER: AtomicU64 = AtomicU64::new(1);

/// 生成递增的请求ID
/// 使用Relaxed内存顺序优化性能
#[inline]
fn next_request_id() -> u64 {
    REQUEST_ID_COUNTER.fetch_add(1, Ordering::Relaxed)
}

pub struct StandExecutionEngine {
    pub strategy: OnceCell<Arc<Box<dyn Strategy>>>,
    pub rests: Rests,
    pub ws_api: WsAPI,
    pub task_count: usize,
    pub tx: async_channel::Sender<ExecutionCommand>,
    pub rx: async_channel::Receiver<ExecutionCommand>,
}

#[allow(unused_mut)]
impl StandExecutionEngine {
    pub async fn new(configs: Vec<ExParams>, task_count: usize) -> Result<Self> {
        let (tx, rx) = async_channel::unbounded::<ExecutionCommand>();

        // 使用32作为WS API任务数（2的幂次方，便于位运算优化）
        const OPTIMAL_WS_API_TASK_COUNT: usize = 16;
        let ws_api = WsAPI::new(configs.clone(), OPTIMAL_WS_API_TASK_COUNT).await?;
        let rests = Rests::new(configs).await;

        Ok(StandExecutionEngine {
            strategy: OnceCell::new(),
            rests,
            ws_api,
            task_count,
            tx,
            rx,
        })
    }
}

impl ExecutionEngine for StandExecutionEngine {
    async fn start(&self) -> quant_common::Result<()> {
        if self.task_count == 0 {
            return Err(qerror!("ExecutionEngine task count is 0"));
        }
        for _ in 0..self.task_count {
            let rx = self.rx.clone();
            let inner_clone = self.rests.clone();
            let handler_clone = self.strategy.get().unwrap().clone();
            tokio::task::spawn(async move {
                while let Ok(cmd) = rx.recv().await {
                    match inner_clone.handle_async_ex_command(cmd).await {
                        Ok(event) => {
                            if let Err(e) = handler_clone.handle_event(event).await {
                                error!("Execution 返回结果失败, {e}");
                            }
                        }
                        Err(e) => error!("Execution command error: {e:?}"),
                    }
                }
            });
        }

        // 创建策略包装器（避免重复创建）
        let strategy_wrapper = ws_api::StrategyWrapper::new(
            self.strategy.get().unwrap().clone(),
            self.rests.exchanges.clone(),
        );

        // 为每个账户的每个任务创建独立的通道处理器
        for (ws_api, account_id) in self.ws_api.apis.clone() {
            let task_count = self.ws_api.get_task_count(account_id);
            for task_idx in 0..task_count {
                // 安全的索引访问
                if let Some(rx) = self.ws_api.rxs[account_id].get(task_idx) {
                    let rx = rx.clone();
                    let ws_api = ws_api.clone();
                    let handler_clone = strategy_wrapper.clone();
                    tokio::task::spawn(async move {
                        if let Err(e) = ws_api.run(account_id, handler_clone, rx).await {
                            error!("ExecutionEngine ws_api error: {e:?}");
                        }
                    });
                }
            }
        }

        Ok(())
    }

    async fn set_strategy(&self, strategy: Box<dyn Strategy>) {
        if self.strategy.set(Arc::new(strategy)).is_err() {
            panic!("ExecutionEngine set_strategy");
        }
    }

    #[inline]
    async fn execution_rest<F, Fut, R>(&self, index: usize, action: F) -> Option<R>
    where
        F: FnOnce(Arc<ExchangeRest>) -> Fut + Send + 'static,
        Fut: std::future::Future<Output = R> + Send + 'static,
        R: Send + 'static,
    {
        if let Some(rest) = self.rests.accounts.get(index) {
            let rest = Arc::clone(rest);
            Some(action(rest).await)
        } else {
            error!("Invalid index: {}", index);
            None
        }
    }

    #[inline]
    async fn execution_async(&self, mut cmd: ExecutionCommand) -> quant_common::Result<()> {
        if let Some(latency) = cmd.context.latency.as_mut() {
            latency.record(Milestone::StrategyEnd);
        }
        let account_id = cmd.account_id;
        if self.ws_api.is_supported(account_id) {
            let req_id = cmd.context.request_id.unwrap_or(next_request_id());
            if let Command::Async(mut cmd) = cmd.cmd {
                match &mut cmd {
                    AsyncCommand::PlaceOrder((order, params)) => {
                        let exchange = self.rests.exchanges[account_id];
                        process_order(order, params, exchange).await?;
                    }
                    AsyncCommand::BatchPlaceOrder((orders, params)) => {
                        let exchange = self.rests.exchanges[account_id];
                        for order in orders {
                            process_order(order, params, exchange).await?;
                        }
                    }
                    AsyncCommand::PlaceOrderExt(params) => {
                        let exchange = self.rests.exchanges[account_id];
                        process_order(&mut params.order, &params.params, exchange).await?;
                    }
                    AsyncCommand::BatchPlaceOrderExt(params) => {
                        let exchange = self.rests.exchanges[account_id];
                        for order in &mut params.orders {
                            process_order(order, &params.params, exchange).await?;
                        }
                    }
                    // 修改订单
                    AsyncCommand::AmendOrder(order) => {
                        let exchange = self.rests.exchanges[account_id];
                        process_order(order, &OrderParams::default(), exchange).await?;
                    }
                    AsyncCommand::AmendOrderExt(params) => {
                        let exchange = self.rests.exchanges[account_id];
                        process_order(&mut params.order, &OrderParams::default(), exchange).await?;
                    }

                    _ => {}
                }
                if let Err(e) = self
                    .ws_api
                    .send_round_robin(account_id, (req_id, cmd.to_async_cmd()?))
                    .await
                {
                    error!("Execution async ws api command handle error: {:?}", e);
                }
            } else {
                error!(
                    "Execution async ws api command handle error, expect async command, found sync command, {:?}",
                    cmd
                );
            }
        } else if let Err(e) = self.tx.send(cmd).await {
            error!("Execution async rest api command handle error: {:?}", e);
        }
        Ok(())
    }
}

#[derive(Clone)]
pub struct Rests {
    pub accounts: Vec<Arc<ExchangeRest>>,
    pub exchanges: Vec<Exchange>,
}

impl Rests {
    pub async fn new(configs: Vec<ExParams>) -> Self {
        let mut accounts = vec![];
        let mut exchanges = vec![];
        for config in configs {
            exchanges.push(config.config.exchange);
            let account = create_private_rest(config.config).await;
            accounts.push(Arc::new(account));
        }
        Self {
            accounts,
            exchanges,
        }
    }

    #[inline]
    pub async fn handle_async_ex_command(&self, mut command: ExecutionCommand) -> Result<Event> {
        let latency = &mut command.context.latency;
        if let Some(l) = latency.as_mut() {
            l.record(Milestone::ExCommandBegin)
        }

        match command.cmd {
            Command::Async(AsyncCommand::PlaceOrder(order)) => {
                let order_clone = order.0.clone();
                let result = self.accounts[command.account_id]
                    .post_order(order.0, order.1)
                    .await;
                if let Some(latency) = command.context.latency.as_mut() {
                    latency.record(Milestone::ExPlaceOrderEnd);
                }
                Ok(Event::ExecutionResult(ExecutionAsyncCommandResult {
                    exchange: self.exchanges[command.account_id],
                    account_id: command.account_id,
                    context: command.context,
                    result: AsyncCmdResult::PlaceOrder(PlaceOrderResult {
                        result,
                        order: order_clone,
                    }),
                }))
            }
            Command::Async(AsyncCommand::PlaceOrderExt(params)) => {
                let order = params.order.clone();
                let result = self.accounts[command.account_id]
                    .post_order_ext(params)
                    .await;
                if let Some(latency) = command.context.latency.as_mut() {
                    latency.record(Milestone::ExPlaceOrderEnd);
                }
                Ok(Event::ExecutionResult(ExecutionAsyncCommandResult {
                    exchange: self.exchanges[command.account_id],
                    account_id: command.account_id,
                    context: command.context,
                    result: AsyncCmdResult::PlaceOrder(PlaceOrderResult { result, order }),
                }))
            }
            Command::Async(AsyncCommand::BatchPlaceOrder(orders)) => {
                let result = self.accounts[command.account_id]
                    .post_batch_order(orders.0, orders.1)
                    .await;
                if let Some(latency) = command.context.latency.as_mut() {
                    latency.record(Milestone::ExPlaceOrderEnd);
                }
                Ok(Event::ExecutionResult(ExecutionAsyncCommandResult {
                    exchange: self.exchanges[command.account_id],
                    account_id: command.account_id,
                    context: command.context,
                    result: AsyncCmdResult::BatchPlaceOrder(result),
                }))
            }
            Command::Async(AsyncCommand::BatchPlaceOrderExt(params)) => {
                let result = self.accounts[command.account_id]
                    .post_batch_order_ext(params)
                    .await;
                Ok(Event::ExecutionResult(ExecutionAsyncCommandResult {
                    exchange: self.exchanges[command.account_id],
                    account_id: command.account_id,
                    context: command.context,
                    result: AsyncCmdResult::BatchPlaceOrder(result),
                }))
            }
            Command::Async(AsyncCommand::CancelOrder(params)) => {
                let result = self.accounts[command.account_id]
                    .cancel_order(params.1.clone(), params.0.clone())
                    .await;
                if let Some(latency) = command.context.latency.as_mut() {
                    latency.record(Milestone::ExCancelEnd);
                }
                Ok(Event::ExecutionResult(ExecutionAsyncCommandResult {
                    exchange: self.exchanges[command.account_id],
                    account_id: command.account_id,
                    context: command.context,
                    result: AsyncCmdResult::CancelOrder(CancelOrderResult {
                        result,
                        order_id: params.0,
                        symbol: params.1,
                    }),
                }))
            }
            Command::Async(AsyncCommand::CancelOrderExt(params)) => {
                let result = self.accounts[command.account_id]
                    .cancel_order_ext(params.clone())
                    .await;
                if let Some(latency) = command.context.latency.as_mut() {
                    latency.record(Milestone::ExCancelEnd);
                }
                Ok(Event::ExecutionResult(ExecutionAsyncCommandResult {
                    exchange: self.exchanges[command.account_id],
                    account_id: command.account_id,
                    context: command.context,
                    result: AsyncCmdResult::CancelOrder(CancelOrderResult {
                        result,
                        order_id: params.order_id,
                        symbol: params.symbol,
                    }),
                }))
            }
            Command::Async(AsyncCommand::BatchCancelOrder(symbol)) => {
                let result = self.accounts[command.account_id]
                    .batch_cancel_order(symbol)
                    .await;
                if let Some(latency) = command.context.latency.as_mut() {
                    latency.record(Milestone::ExCancelEnd);
                }
                Ok(Event::ExecutionResult(ExecutionAsyncCommandResult {
                    exchange: self.exchanges[command.account_id],
                    account_id: command.account_id,
                    context: command.context,
                    result: AsyncCmdResult::BatchCancelOrder(result),
                }))
            }
            Command::Async(AsyncCommand::BatchCancelOrderExt(params)) => {
                let result = self.accounts[command.account_id]
                    .batch_cancel_order_ext(params)
                    .await;
                Ok(Event::ExecutionResult(ExecutionAsyncCommandResult {
                    exchange: self.exchanges[command.account_id],
                    account_id: command.account_id,
                    context: command.context,
                    result: AsyncCmdResult::BatchCancelOrder(result),
                }))
            }
            Command::Async(AsyncCommand::AmendOrder(order)) => {
                let order_clone = order.clone();
                let result = self.accounts[command.account_id]
                    .amend_order(order.clone())
                    .await;
                if let Some(latency) = command.context.latency.as_mut() {
                    latency.record(Milestone::ExAmendEnd);
                }
                Ok(Event::ExecutionResult(ExecutionAsyncCommandResult {
                    exchange: self.exchanges[command.account_id],
                    account_id: command.account_id,
                    context: command.context,
                    result: AsyncCmdResult::AmendOrder(AmendOrderResult {
                        result,
                        order: order_clone,
                    }),
                }))
            }
            Command::Async(AsyncCommand::AmendOrderExt(order)) => {
                let result = self.accounts[command.account_id]
                    .amend_order_ext(order.clone())
                    .await;
                if let Some(latency) = command.context.latency.as_mut() {
                    latency.record(Milestone::ExAmendEnd);
                }
                Ok(Event::ExecutionResult(ExecutionAsyncCommandResult {
                    exchange: self.exchanges[command.account_id],
                    account_id: command.account_id,
                    context: command.context,
                    result: AsyncCmdResult::AmendOrder(AmendOrderResult {
                        result,
                        order: order.order,
                    }),
                }))
            }
            Command::Async(AsyncCommand::BatchCancelOrderById(ids)) => {
                let result = self.accounts[command.account_id]
                    .batch_cancel_order_by_ids(ids.0, ids.1, ids.2)
                    .await;
                Ok(Event::ExecutionResult(ExecutionAsyncCommandResult {
                    exchange: self.exchanges[command.account_id],
                    account_id: command.account_id,
                    context: command.context,
                    result: AsyncCmdResult::BatchCancelOrderByIds(result),
                }))
            }
            Command::Async(AsyncCommand::BatchCancelOrderByIdExt(params)) => {
                let result = self.accounts[command.account_id]
                    .batch_cancel_order_by_ids_ext(params)
                    .await;
                Ok(Event::ExecutionResult(ExecutionAsyncCommandResult {
                    exchange: self.exchanges[command.account_id],
                    account_id: command.account_id,
                    context: command.context,
                    result: AsyncCmdResult::BatchCancelOrderByIds(result),
                }))
            }
            _ => Err(qerror!("Unsupported async command: {:?}", command)),
        }
    }
}
