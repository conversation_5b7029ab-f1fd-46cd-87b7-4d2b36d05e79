use crate::model::{account::AccountId, context::Context};
use quant_common::base::traits::model::*;
use quant_common::base::*;
use quant_common::{Result, qerror};
use serde::{Deserialize, Serialize};
use serde_json::Value;

#[derive(Debug, Serialize, Deserialize)]
pub struct ExecutionCommand {
    pub account_id: AccountId,
    #[serde(default, skip)]
    pub context: Context,
    pub cmd: Command,
}

#[allow(clippy::large_enum_variant)]
#[derive(Debug, Serialize, Deserialize)]
pub enum Command {
    Sync(SyncCommand),
    Async(AsyncCommand),
}

#[derive(Debug, Serialize, Deserialize)]
pub enum SyncCommand {
    // -----------用户自定义接口-------------------
    /// 自定义请求
    Request(UserRequest),

    // -----------私有接口-------------------
    // ---订单---
    /// 获取当前Symbol的历史订单
    GetOrders((Symbol, i64, i64)),
    /// 获取当前Symbol的历史订单（扩展接口）
    GetOrdersExt(GetOrdersParams),
    /// 获取当前Symbol的挂单
    GetOpenOrders(Symbol),
    /// 获取当前Symbol的挂单（扩展接口）
    GetOpenOrdersExt(GetOpenOrdersParams),
    /// 获取所有Symbol的挂单
    GetAllOpenOrders,
    /// 获取所有Symbol的挂单（扩展接口）
    GetAllOpenOrdersExt(GetAllOpenOrdersParams),
    /// 获取订单
    GetOrderById((Symbol, OrderId)),
    /// 获取订单（扩展接口）
    GetOrderByIdExt(GetOrderByIdParams),
    /// 下单
    PlaceOrder((Order, OrderParams)),
    /// 下单（扩展接口）
    PlaceOrderExt(PostOrderParams),
    /// 批量下单
    BatchPlaceOrder((Vec<Order>, OrderParams)),
    /// 批量下单（扩展接口）
    BatchPlaceOrderExt(PostBatchOrderParams),
    /// 修改订单
    AmendOrder(Order),
    /// 修改订单拓展接口
    AmendOrderExt(AmendOrderParams),
    /// 撤单
    CancelOrder((OrderId, Symbol)),
    /// 撤单拓展接口
    CancelOrderExt(CancelOrderParams),
    /// 批量撤单
    BatchCancelOrder(Symbol),
    /// 批量撤单（扩展接口）
    BatchCancelOrderExt(BatchCancelOrderParams),
    /// 批量撤单根据订单ID
    BatchCancelOrderById((Option<Symbol>, Option<Vec<String>>, Option<Vec<String>>)),
    /// 批量撤单根据订单ID（扩展接口）
    BatchCancelOrderByIdExt(BatchCancelOrderByIdsParams),

    // ---账户---
    /// 查询持仓
    Position(Option<Symbol>),
    /// 查询持仓（扩展接口）
    PositionExt(GetPositionParams),
    /// 查询所有持仓
    Positions,
    /// 查询所有持仓（扩展接口）
    PositionsExt(GetPositionsParams),
    /// 获取最大持仓
    MaxPosition((Symbol, u8)),
    /// 获取最大持仓（扩展接口）
    MaxPositionExt(GetMaxPositionParams),
    /// 查询结算资金费
    FundingFee((Symbol, Option<i64>, Option<i64>)),
    /// 查询结算资金费（扩展接口）
    FundingFeeExt(GetFundingFeeParams),
    /// 最大杠杆
    MaxLeverage(Symbol),
    /// 最大杠杆（扩展接口）
    MaxLeverageExt(GetMaxLeverageParams),
    /// 查询保证金模式
    MarginMode((Symbol, String)),
    /// 查询保证金模式（扩展接口）
    MarginModeExt(GetMarginModeParams),
    /// 设置保证金模式
    SetMarginMode((Symbol, String, MarginMode)),
    /// 设置保证金模式（扩展接口）
    SetMarginModeExt(SetMarginModeParams),
    /// 查询USDT余额
    UsdtBalance,
    UsdtBalanceExt(GetUsdtBalanceParams),
    /// 查询多币种余额
    Balance,
    /// 查询多币种余额（扩展接口）
    BalanceExt(GetBalancesParams),
    /// 查询指定币种余额
    BalanceByCoin(String),
    /// 查询指定币种余额（扩展接口）
    BalanceByCoinExt(GetBalanceParams),
    /// 获取手续费
    FeeRate(Symbol),
    /// 获取手续费（扩展接口）
    FeeRateExt(GetFeeRateParams),
    /// 设置杠杆
    SetLeverage((Symbol, u8)),
    /// 设置杠杆（扩展接口）
    SetLeverageExt(SetLeverageParams),
    /// 查询是否双向持仓
    IsDualSidePosition,
    /// 查询是否双向持仓拓展接口
    IsDualSidePositionExt(IsDualSideParams),
    /// 设置双向持仓
    SetDualSidePosition(bool),
    /// 设置双向持仓拓展接口
    SetDualSidePositionExt(SetDualSideParams),
    /// 万向划转
    Transfer(Transfer),
    /// 万向划转（扩展接口）
    TransferExt(TransferParams),
    /// 子账户划转
    SubTransfer(SubTransfer),
    /// 子账户划转（扩展接口）
    SubTransferExt(SubTransferParams),
    /// 获取充值地址
    GetDepositAddress((String, Option<Chain>, Option<f64>)),
    /// 获取充值地址（扩展接口）
    GetDepositAddressExt(GetDepositAddressParams),
    /// 提币
    Withdrawal(WithDrawlParams),
    /// 提币（扩展接口）
    WithdrawalExt(WithdrawalParams),
    /// 获取账户信息
    GetAccountInfo,
    /// 获取账户信息（扩展接口）
    GetAccountInfoExt(GetAccountInfoParams),
    /// 获取账户模式
    GetAccountMode,
    /// 获取账户模式（扩展接口）
    GetAccountModeExt(GetAccountModeParams),
    /// 设置账户模式
    SetAccountMode(AccountMode),
    /// 设置账户模式（扩展接口）
    SetAccountModeExt(SetAccountModeParams),
    /// 获取用户ID
    GetUserId,
    /// 获取用户ID（扩展接口）
    GetUserIdExt(GetUserIdParams),
    /// 查询交易所折扣信息
    GetFeeDiscountInfo,
    /// 查询交易所折扣信息（扩展接口）
    GetFeeDiscountInfoExt(GetFeeDiscountInfoParams),
    /// 查询账户是否开启原生币手续费折扣
    IsFeeDiscountEnabled,
    /// 查询账户是否开启原生币手续费折扣（扩展接口）
    IsFeeDiscountEnabledExt(IsFeeDiscountEnabledParams),
    /// 开关账户原生币手续费折扣
    SetFeeDiscountEnabled(bool),
    /// 开关账户原生币手续费折扣（扩展接口）
    SetFeeDiscountEnabledExt(SetFeeDiscountEnabledParams),

    /// 借币
    Borrow((String, f64)),
    /// 借币（扩展接口）
    BorrowExt(BorrowCoinParams),
    /// 查询借币
    GetBorrowed(Option<String>),
    /// 查询借币（扩展接口）
    GetBorrowedExt(GetBorrowParams),
    /// 还币
    Repay((String, f64)),
    /// 还币（扩展接口）
    RepayExt(RepayCoinParams),
    /// 获取借贷利率
    GetBorrowRate(Option<String>),
    /// 获取借贷利率（扩展接口）
    GetBorrowRateExt(GetBorrowRateParams),
    /// 获取借贷限额
    GetBorrowLimit((Option<bool>, String)),
    /// 获取借贷限额（扩展接口）
    GetBorrowLimitExt(GetBorrowLimitsParams),

    // -----------公有接口-------------------
    /// 查询24h交易信息（单个交易对）
    Ticker(Option<Symbol>),
    /// 查询24h交易信息（单个交易对）（扩展接口）
    TickerExt(GetTickerParams),
    /// 查询所有交易对的24h交易信息
    Tickers,
    /// 查询所有交易对的24h交易信息（扩展接口）
    TickersExt(GetTickersParams),
    /// 查询最佳买卖价（单个交易对）
    Bbo(Option<Symbol>),
    /// 查询最佳买卖价（单个交易对）（扩展接口）
    BboExt(GetBboTickerParams),
    /// 查询所有交易对的最佳买卖价
    BboTickers,
    /// 查询所有交易对的最佳买卖价（扩展接口）
    BboTickersExt(GetBboTickersParams),
    /// 查询深度
    Depth((Symbol, Option<u32>)),
    /// 查询深度（扩展接口）
    DepthExt(GetDepthParams),
    /// 查询产品信息
    Instrument(Option<Symbol>),
    /// 查询产品信息（扩展接口）
    InstrumentExt(GetInstrumentParams),
    /// 查询所有产品信息
    Instruments,
    /// 查询所有产品信息（扩展接口）
    InstrumentsExt(GetInstrumentsParams),
    /// 查询资金费率
    FundingRate,
    /// 查询单个交易对资金费率
    FundingRateBySymbol(Symbol),
    /// 查询单个交易对资金费率（扩展接口）
    FundingRateBySymbolExt(GetFundingRateParams),
    /// 查询资金费率（扩展接口）
    FundingRateExt(GetFundingRatesParams),
    /// 查询资金费率历史
    FundingRateHistory(GetFundingRateHistoryParams),
    /// 标记价格
    MarkPrice(Option<Symbol>),
    /// 标记价格（扩展接口）
    MarkPriceExt(GetMarkPriceParams),
    /// K线数据
    KlineExt(GetKlineParams),

    /// Dex命令
    Dex(Value),
}

#[derive(Debug)]
pub struct ExecutionAsyncCommandResult {
    pub exchange: Exchange,
    pub account_id: AccountId,
    pub context: Context,
    pub result: AsyncCmdResult,
}

#[derive(Debug, Serialize, Deserialize)]
pub enum AsyncCommand {
    /// 下单
    PlaceOrder((Order, OrderParams)),
    PlaceOrderExt(PostOrderParams),
    /// 批量下单
    BatchPlaceOrder((Vec<Order>, OrderParams)),
    BatchPlaceOrderExt(PostBatchOrderParams),
    /// 修改订单
    AmendOrder(Order),
    AmendOrderExt(AmendOrderParams),
    /// 撤单
    CancelOrder((OrderId, Symbol)),
    CancelOrderExt(CancelOrderParams),
    /// 批量撤单
    BatchCancelOrder(Symbol),
    BatchCancelOrderExt(BatchCancelOrderParams),
    /// 批量撤单根据订单ID
    BatchCancelOrderById((Option<Symbol>, Option<Vec<String>>, Option<Vec<String>>)),
    BatchCancelOrderByIdExt(BatchCancelOrderByIdsParams),
    /// Dex命令
    Dex(Value),
}

impl AsyncCommand {
    #[inline]
    pub fn to_async_cmd(self) -> Result<AsyncCmd> {
        let cmd = match self {
            AsyncCommand::PlaceOrder(order) => AsyncCmd::PlaceOrder(PlaceOrderCmd {
                order: order.0,
                params: order.1,
            }),
            AsyncCommand::PlaceOrderExt(params) => AsyncCmd::PlaceOrder(PlaceOrderCmd {
                order: params.order,
                params: params.params,
            }),
            AsyncCommand::BatchPlaceOrder(orders) => {
                AsyncCmd::BatchPlaceOrder(BatchPlaceOrderCmd {
                    orders: orders.0,
                    params: orders.1,
                })
            }
            AsyncCommand::BatchPlaceOrderExt(params) => {
                AsyncCmd::BatchPlaceOrder(BatchPlaceOrderCmd {
                    orders: params.orders,
                    params: params.params,
                })
            }
            AsyncCommand::AmendOrder(order) => AsyncCmd::AmendOrder(order),
            AsyncCommand::AmendOrderExt(params) => AsyncCmd::AmendOrder(params.order),
            AsyncCommand::CancelOrder(order) => AsyncCmd::CancelOrder(CancelOrderCmd {
                order_id: order.0,
                symbol: order.1,
            }),
            AsyncCommand::CancelOrderExt(params) => AsyncCmd::CancelOrder(CancelOrderCmd {
                order_id: params.order_id,
                symbol: params.symbol,
            }),
            AsyncCommand::BatchCancelOrder(symbol) => AsyncCmd::BatchCancelOrder(symbol),
            AsyncCommand::BatchCancelOrderExt(params) => AsyncCmd::BatchCancelOrder(params.symbol),
            AsyncCommand::BatchCancelOrderById((symbol, order_ids, order_ids_by_symbol)) => {
                AsyncCmd::BatchCancelOrderByIds(BatchCancelOrderByIdsCmd {
                    symbol,
                    ids: order_ids,
                    cids: order_ids_by_symbol,
                })
            }
            AsyncCommand::BatchCancelOrderByIdExt(params) => {
                AsyncCmd::BatchCancelOrderByIds(BatchCancelOrderByIdsCmd {
                    symbol: params.symbol,
                    ids: params.ids,
                    cids: params.cids,
                })
            }
            AsyncCommand::Dex(_) => {
                return Err(qerror!(
                    "Dex command is not supported, please use AsyncDex instead"
                ));
            }
        };
        Ok(cmd)
    }
}
