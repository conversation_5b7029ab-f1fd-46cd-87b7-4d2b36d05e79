use account::AccountEvent;
use algo_common::msg::AsyncCmd;
use ex_command::{ExecutionAsyncCommandResult, ExecutionCommand};
use extras::ExtrasEvent;
use market::MarketEvent;
use net::NetEvent;
use serde::{Deserialize, Serialize};
use serde_json::Value;
use timer::TimerEvent;

pub mod account;
pub mod ex_command;
pub mod ex_command_flatten;
pub mod extras;
pub mod market;
pub mod net;
pub mod timer;

#[derive(Debug)]
pub enum Event {
    Market(MarketEvent),
    Account(AccountEvent),
    Execution(ExecutionCommand),
    ExecutionResult(ExecutionAsyncCommandResult),
    Timer(TimerEvent),
    Net(NetEvent),
    Command(AsyncCmd),
    Extras(ExtrasEvent),
    System(SystemCommand),
}

#[derive(Clone, Debug, Deserialize, Serialize)]
pub enum SystemCommand {
    /// 停止
    Stop,
    /// 热更新
    HotUpdate(Value),
}
