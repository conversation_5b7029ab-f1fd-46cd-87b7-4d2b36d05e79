use serde_json::Value;

use crate::model::{account::AccountId, context::Context};

use super::ex_command::{AsyncCommand, Command, ExecutionCommand, SyncCommand};
use quant_common::{
    Result,
    base::{
        Order, OrderId, OrderParams, Symbol, UserRequest,
        traits::{
            AmendOrderParams, BatchCancelOrderByIdsParams, BatchCancelOrderParams,
            BorrowCoinParams, CancelOrderParams, GetAccountInfoParams, GetAccountModeParams,
            GetAllOpenOrdersParams, GetBalanceParams, GetBalancesParams, GetBboTickerParams,
            GetBboTickersParams, GetBorrowLimitsParams, GetBorrowParams, GetBorrowRateParams,
            GetDepositAddressParams, GetDepthParams, GetFeeDiscountInfoParams, GetFeeRateParams,
            GetFundingFeeParams, GetFundingRateHistoryParams, GetFundingRateParams,
            GetFundingRatesParams, GetInstrumentParams, GetInstrumentsParams, GetKlineParams,
            GetMarginModeParams, GetMarkPriceParams, GetMaxLeverageParams, GetMaxPositionParams,
            GetOpenOrdersParams, GetOrderByIdParams, GetOrdersParams, GetPositionParams,
            GetPositionsParams, GetTickerParams, GetTickersParams, GetUsdtBalanceParams,
            GetUserIdParams, IsDualSideParams, IsFeeDiscountEnabledParams, PostBatchOrderParams,
            PostOrderParams, RepayCoinParams, SetAccountModeParams, SetDualSideParams,
            SetFeeDiscountEnabledParams, SetLeverageParams, SetMarginModeParams, SubTransferParams,
            TransferParams, WithdrawalParams,
        },
    },
    qerror,
};

// 扁平化格式序列化/反序列化
impl ExecutionCommand {
    // 辅助函数：从JSON获取extra，如果不存在则返回空HashMap
    #[inline]
    fn get_extra_or_empty(
        obj: &serde_json::Map<String, Value>,
    ) -> Result<std::collections::HashMap<String, Value>> {
        match obj.get("extra") {
            Some(extra) => serde_json::from_value(extra.clone())
                .map_err(|e| qerror!("解析extra字段失败: {}", e)),
            None => Ok(std::collections::HashMap::new()),
        }
    }

    // 辅助函数：从JSON获取sync，如果不存在则返回false
    #[inline]
    fn get_sync_or_false(obj: &serde_json::Map<String, Value>) -> bool {
        obj.get("sync").and_then(|v| v.as_bool()).unwrap_or(false)
    }

    /// 只在extra非空时添加到对象中
    #[inline]
    fn add_extra(
        obj: &mut serde_json::Map<String, Value>,
        extra: &std::collections::HashMap<String, Value>,
    ) -> Result<()> {
        if !extra.is_empty() {
            obj.insert("extra".to_string(), serde_json::to_value(extra)?);
        }
        Ok(())
    }

    /// 将ExecutionCommand序列化为扁平化格式: {"method": "方法名", "sync": true/false, ...参数}
    pub fn to_flatten(&self) -> Result<Value> {
        // 创建基础对象，包含方法名和sync/async标志
        let mut obj = serde_json::Map::with_capacity(10); // 预分配一个合理的容量

        // 添加account_id
        obj.insert(
            "account_id".to_string(),
            serde_json::to_value(self.account_id)?,
        );

        // 根据Command类型获取命令名称、sync/async标志和参数
        match &self.cmd {
            Command::Sync(sync_cmd) => {
                match sync_cmd {
                    SyncCommand::Request(req) => {
                        obj.insert("method".to_string(), Value::String("Request".to_string()));
                        obj.insert("sync".to_string(), Value::Bool(true));
                        obj.insert("request".to_string(), serde_json::to_value(req)?);
                    }
                    SyncCommand::GetOrdersExt(args) => {
                        obj.insert("method".to_string(), Value::String("GetOrders".to_string()));
                        obj.insert("sync".to_string(), Value::Bool(true));
                        obj.insert("symbol".to_string(), serde_json::to_value(&args.symbol)?);
                        obj.insert(
                            "start_time".to_string(),
                            serde_json::to_value(args.start_time)?,
                        );
                        obj.insert("end_time".to_string(), serde_json::to_value(args.end_time)?);
                        Self::add_extra(&mut obj, &args.extra)?;
                    }
                    SyncCommand::GetOpenOrdersExt(args) => {
                        obj.insert(
                            "method".to_string(),
                            Value::String("GetOpenOrders".to_string()),
                        );
                        obj.insert("sync".to_string(), Value::Bool(true));
                        obj.insert("symbol".to_string(), serde_json::to_value(&args.symbol)?);
                        Self::add_extra(&mut obj, &args.extra)?;
                    }
                    SyncCommand::GetAllOpenOrdersExt(args) => {
                        obj.insert(
                            "method".to_string(),
                            Value::String("GetAllOpenOrders".to_string()),
                        );
                        obj.insert("sync".to_string(), Value::Bool(true));
                        Self::add_extra(&mut obj, &args.extra)?;
                    }
                    SyncCommand::GetOrderByIdExt(args) => {
                        obj.insert(
                            "method".to_string(),
                            Value::String("GetOrderById".to_string()),
                        );
                        obj.insert("sync".to_string(), Value::Bool(true));
                        obj.insert("symbol".to_string(), serde_json::to_value(&args.symbol)?);
                        obj.insert(
                            "order_id".to_string(),
                            serde_json::to_value(&args.order_id)?,
                        );
                        Self::add_extra(&mut obj, &args.extra)?;
                    }
                    SyncCommand::PlaceOrderExt(args) => {
                        obj.insert(
                            "method".to_string(),
                            Value::String("PlaceOrder".to_string()),
                        );
                        obj.insert("sync".to_string(), Value::Bool(true));
                        obj.insert("order".to_string(), serde_json::to_value(&args.order)?);
                        obj.insert("params".to_string(), serde_json::to_value(&args.params)?);
                        Self::add_extra(&mut obj, &args.extra)?;
                    }
                    SyncCommand::BatchPlaceOrderExt(args) => {
                        obj.insert(
                            "method".to_string(),
                            Value::String("BatchPlaceOrder".to_string()),
                        );
                        obj.insert("sync".to_string(), Value::Bool(true));
                        obj.insert("orders".to_string(), serde_json::to_value(&args.orders)?);
                        obj.insert("params".to_string(), serde_json::to_value(&args.params)?);
                        Self::add_extra(&mut obj, &args.extra)?;
                    }
                    SyncCommand::AmendOrderExt(args) => {
                        obj.insert(
                            "method".to_string(),
                            Value::String("AmendOrder".to_string()),
                        );
                        obj.insert("sync".to_string(), Value::Bool(true));
                        obj.insert("order".to_string(), serde_json::to_value(&args.order)?);
                        Self::add_extra(&mut obj, &args.extra)?;
                    }
                    SyncCommand::CancelOrderExt(args) => {
                        obj.insert(
                            "method".to_string(),
                            Value::String("CancelOrder".to_string()),
                        );
                        obj.insert("sync".to_string(), Value::Bool(true));
                        obj.insert("symbol".to_string(), serde_json::to_value(&args.symbol)?);
                        obj.insert(
                            "order_id".to_string(),
                            serde_json::to_value(&args.order_id)?,
                        );
                        Self::add_extra(&mut obj, &args.extra)?;
                    }
                    SyncCommand::BatchCancelOrderExt(args) => {
                        obj.insert(
                            "method".to_string(),
                            Value::String("BatchCancelOrder".to_string()),
                        );
                        obj.insert("sync".to_string(), Value::Bool(true));
                        obj.insert("symbol".to_string(), serde_json::to_value(&args.symbol)?);
                        Self::add_extra(&mut obj, &args.extra)?;
                    }
                    SyncCommand::BatchCancelOrderByIdExt(args) => {
                        obj.insert(
                            "method".to_string(),
                            Value::String("BatchCancelOrderById".to_string()),
                        );
                        obj.insert("sync".to_string(), Value::Bool(true));
                        obj.insert("symbol".to_string(), serde_json::to_value(&args.symbol)?);
                        obj.insert("ids".to_string(), serde_json::to_value(&args.ids)?);
                        obj.insert("cids".to_string(), serde_json::to_value(&args.cids)?);
                        Self::add_extra(&mut obj, &args.extra)?;
                    }
                    SyncCommand::PositionExt(args) => {
                        obj.insert("method".to_string(), Value::String("Position".to_string()));
                        obj.insert("sync".to_string(), Value::Bool(true));
                        obj.insert("symbol".to_string(), serde_json::to_value(&args.symbol)?);
                        Self::add_extra(&mut obj, &args.extra)?;
                    }
                    SyncCommand::PositionsExt(args) => {
                        obj.insert("method".to_string(), Value::String("Positions".to_string()));
                        obj.insert("sync".to_string(), Value::Bool(true));
                        Self::add_extra(&mut obj, &args.extra)?;
                    }
                    SyncCommand::MaxPositionExt(args) => {
                        obj.insert(
                            "method".to_string(),
                            Value::String("MaxPosition".to_string()),
                        );
                        obj.insert("sync".to_string(), Value::Bool(true));
                        obj.insert("symbol".to_string(), serde_json::to_value(&args.symbol)?);
                        Self::add_extra(&mut obj, &args.extra)?;
                    }
                    SyncCommand::FundingFeeExt(args) => {
                        obj.insert(
                            "method".to_string(),
                            Value::String("FundingFee".to_string()),
                        );
                        obj.insert("sync".to_string(), Value::Bool(true));
                        obj.insert("symbol".to_string(), serde_json::to_value(&args.symbol)?);
                        Self::add_extra(&mut obj, &args.extra)?;
                    }
                    SyncCommand::MaxLeverageExt(args) => {
                        obj.insert(
                            "method".to_string(),
                            Value::String("MaxLeverage".to_string()),
                        );
                        obj.insert("sync".to_string(), Value::Bool(true));
                        obj.insert("symbol".to_string(), serde_json::to_value(&args.symbol)?);
                        Self::add_extra(&mut obj, &args.extra)?;
                    }
                    SyncCommand::MarginModeExt(args) => {
                        obj.insert(
                            "method".to_string(),
                            Value::String("MarginMode".to_string()),
                        );
                        obj.insert("sync".to_string(), Value::Bool(true));
                        obj.insert("symbol".to_string(), serde_json::to_value(&args.symbol)?);
                        Self::add_extra(&mut obj, &args.extra)?;
                    }
                    SyncCommand::SetMarginModeExt(args) => {
                        obj.insert(
                            "method".to_string(),
                            Value::String("SetMarginMode".to_string()),
                        );
                        obj.insert("sync".to_string(), Value::Bool(true));
                        obj.insert("symbol".to_string(), serde_json::to_value(&args.symbol)?);
                        obj.insert(
                            "margin_mode".to_string(),
                            serde_json::to_value(&args.margin_mode)?,
                        );
                        obj.insert(
                            "margin_coin".to_string(),
                            serde_json::to_value(&args.margin_coin)?,
                        );
                        Self::add_extra(&mut obj, &args.extra)?;
                    }
                    SyncCommand::UsdtBalanceExt(args) => {
                        obj.insert(
                            "method".to_string(),
                            Value::String("UsdtBalance".to_string()),
                        );
                        obj.insert("sync".to_string(), Value::Bool(true));
                        Self::add_extra(&mut obj, &args.extra)?;
                    }
                    SyncCommand::BalanceExt(args) => {
                        obj.insert("method".to_string(), Value::String("Balance".to_string()));
                        obj.insert("sync".to_string(), Value::Bool(true));
                        Self::add_extra(&mut obj, &args.extra)?;
                    }
                    SyncCommand::BalanceByCoinExt(args) => {
                        obj.insert(
                            "method".to_string(),
                            Value::String("BalanceByCoin".to_string()),
                        );
                        obj.insert("sync".to_string(), Value::Bool(true));
                        obj.insert("asset".to_string(), serde_json::to_value(&args.asset)?);
                        Self::add_extra(&mut obj, &args.extra)?;
                    }
                    SyncCommand::FeeRateExt(args) => {
                        obj.insert("method".to_string(), Value::String("FeeRate".to_string()));
                        obj.insert("sync".to_string(), Value::Bool(true));
                        obj.insert("symbol".to_string(), serde_json::to_value(&args.symbol)?);
                        Self::add_extra(&mut obj, &args.extra)?;
                    }
                    SyncCommand::SetLeverageExt(args) => {
                        obj.insert(
                            "method".to_string(),
                            Value::String("SetLeverage".to_string()),
                        );
                        obj.insert("sync".to_string(), Value::Bool(true));
                        obj.insert("symbol".to_string(), serde_json::to_value(&args.symbol)?);
                        obj.insert("leverage".to_string(), serde_json::to_value(args.leverage)?);
                        Self::add_extra(&mut obj, &args.extra)?;
                    }
                    SyncCommand::IsDualSidePositionExt(args) => {
                        obj.insert(
                            "method".to_string(),
                            Value::String("IsDualSidePosition".to_string()),
                        );
                        obj.insert("sync".to_string(), Value::Bool(true));
                        Self::add_extra(&mut obj, &args.extra)?;
                    }
                    SyncCommand::SetDualSidePositionExt(args) => {
                        obj.insert(
                            "method".to_string(),
                            Value::String("SetDualSidePosition".to_string()),
                        );
                        obj.insert("sync".to_string(), Value::Bool(true));
                        obj.insert(
                            "is_dual_side".to_string(),
                            serde_json::to_value(args.is_dual_side)?,
                        );
                        Self::add_extra(&mut obj, &args.extra)?;
                    }
                    SyncCommand::TransferExt(args) => {
                        obj.insert("method".to_string(), Value::String("Transfer".to_string()));
                        obj.insert("sync".to_string(), Value::Bool(true));
                        obj.insert(
                            "transfer".to_string(),
                            serde_json::to_value(&args.transfer)?,
                        );
                        Self::add_extra(&mut obj, &args.extra)?;
                    }
                    SyncCommand::SubTransferExt(args) => {
                        obj.insert(
                            "method".to_string(),
                            Value::String("SubTransfer".to_string()),
                        );
                        obj.insert("sync".to_string(), Value::Bool(true));
                        obj.insert(
                            "transfer".to_string(),
                            serde_json::to_value(&args.transfer)?,
                        );
                        Self::add_extra(&mut obj, &args.extra)?;
                    }
                    SyncCommand::GetDepositAddressExt(args) => {
                        obj.insert(
                            "method".to_string(),
                            Value::String("GetDepositAddress".to_string()),
                        );
                        obj.insert("sync".to_string(), Value::Bool(true));
                        obj.insert("ccy".to_string(), serde_json::to_value(&args.ccy)?);
                        obj.insert("chain".to_string(), serde_json::to_value(&args.chain)?);
                        obj.insert("amount".to_string(), serde_json::to_value(args.amount)?);
                        Self::add_extra(&mut obj, &args.extra)?;
                    }
                    SyncCommand::WithdrawalExt(args) => {
                        obj.insert(
                            "method".to_string(),
                            Value::String("Withdrawal".to_string()),
                        );
                        obj.insert("sync".to_string(), Value::Bool(true));
                        obj.insert("params".to_string(), serde_json::to_value(&args.params)?);
                        Self::add_extra(&mut obj, &args.extra)?;
                    }
                    SyncCommand::GetAccountInfoExt(args) => {
                        obj.insert(
                            "method".to_string(),
                            Value::String("GetAccountInfo".to_string()),
                        );
                        obj.insert("sync".to_string(), Value::Bool(true));
                        Self::add_extra(&mut obj, &args.extra)?;
                    }
                    SyncCommand::GetAccountModeExt(args) => {
                        obj.insert(
                            "method".to_string(),
                            Value::String("GetAccountMode".to_string()),
                        );
                        obj.insert("sync".to_string(), Value::Bool(true));
                        Self::add_extra(&mut obj, &args.extra)?;
                    }
                    SyncCommand::SetAccountModeExt(args) => {
                        obj.insert(
                            "method".to_string(),
                            Value::String("SetAccountMode".to_string()),
                        );
                        obj.insert("sync".to_string(), Value::Bool(true));
                        obj.insert("mode".to_string(), serde_json::to_value(&args.mode)?);
                        Self::add_extra(&mut obj, &args.extra)?;
                    }
                    SyncCommand::GetUserIdExt(args) => {
                        obj.insert("method".to_string(), Value::String("GetUserId".to_string()));
                        obj.insert("sync".to_string(), Value::Bool(true));
                        Self::add_extra(&mut obj, &args.extra)?;
                    }
                    SyncCommand::GetFeeDiscountInfoExt(args) => {
                        obj.insert(
                            "method".to_string(),
                            Value::String("GetFeeDiscountInfo".to_string()),
                        );
                        obj.insert("sync".to_string(), Value::Bool(true));
                        Self::add_extra(&mut obj, &args.extra)?;
                    }
                    SyncCommand::IsFeeDiscountEnabledExt(args) => {
                        obj.insert(
                            "method".to_string(),
                            Value::String("IsFeeDiscountEnabled".to_string()),
                        );
                        obj.insert("sync".to_string(), Value::Bool(true));
                        Self::add_extra(&mut obj, &args.extra)?;
                    }
                    SyncCommand::SetFeeDiscountEnabledExt(args) => {
                        obj.insert(
                            "method".to_string(),
                            Value::String("SetFeeDiscountEnabled".to_string()),
                        );
                        obj.insert("sync".to_string(), Value::Bool(true));
                        obj.insert("enable".to_string(), serde_json::to_value(args.enable)?);
                        Self::add_extra(&mut obj, &args.extra)?;
                    }
                    SyncCommand::BorrowExt(args) => {
                        obj.insert("method".to_string(), Value::String("Borrow".to_string()));
                        obj.insert("sync".to_string(), Value::Bool(true));
                        obj.insert("coin".to_string(), serde_json::to_value(&args.coin)?);
                        obj.insert("amount".to_string(), serde_json::to_value(args.amount)?);
                        Self::add_extra(&mut obj, &args.extra)?;
                    }
                    SyncCommand::GetBorrowedExt(args) => {
                        obj.insert(
                            "method".to_string(),
                            Value::String("GetBorrowed".to_string()),
                        );
                        obj.insert("sync".to_string(), Value::Bool(true));
                        obj.insert("coin".to_string(), serde_json::to_value(&args.coin)?);
                        Self::add_extra(&mut obj, &args.extra)?;
                    }
                    SyncCommand::RepayExt(args) => {
                        obj.insert("method".to_string(), Value::String("Repay".to_string()));
                        obj.insert("sync".to_string(), Value::Bool(true));
                        obj.insert("coin".to_string(), serde_json::to_value(&args.coin)?);
                        obj.insert("amount".to_string(), serde_json::to_value(args.amount)?);
                        Self::add_extra(&mut obj, &args.extra)?;
                    }
                    SyncCommand::GetBorrowRateExt(args) => {
                        obj.insert(
                            "method".to_string(),
                            Value::String("GetBorrowRate".to_string()),
                        );
                        obj.insert("sync".to_string(), Value::Bool(true));
                        obj.insert("coin".to_string(), serde_json::to_value(&args.coin)?);
                        Self::add_extra(&mut obj, &args.extra)?;
                    }
                    SyncCommand::GetBorrowLimitExt(args) => {
                        obj.insert(
                            "method".to_string(),
                            Value::String("GetBorrowLimit".to_string()),
                        );
                        obj.insert("sync".to_string(), Value::Bool(true));
                        obj.insert("coin".to_string(), serde_json::to_value(&args.coin)?);
                        Self::add_extra(&mut obj, &args.extra)?;
                    }
                    SyncCommand::TickerExt(args) => {
                        obj.insert("method".to_string(), Value::String("Ticker".to_string()));
                        obj.insert("sync".to_string(), Value::Bool(true));
                        obj.insert("symbol".to_string(), serde_json::to_value(&args.symbol)?);
                        Self::add_extra(&mut obj, &args.extra)?;
                    }
                    SyncCommand::TickersExt(args) => {
                        obj.insert("method".to_string(), Value::String("Tickers".to_string()));
                        obj.insert("sync".to_string(), Value::Bool(true));
                        Self::add_extra(&mut obj, &args.extra)?;
                    }
                    SyncCommand::BboExt(args) => {
                        obj.insert("method".to_string(), Value::String("Bbo".to_string()));
                        obj.insert("sync".to_string(), Value::Bool(true));
                        obj.insert("symbol".to_string(), serde_json::to_value(&args.symbol)?);
                        Self::add_extra(&mut obj, &args.extra)?;
                    }
                    SyncCommand::BboTickersExt(args) => {
                        obj.insert(
                            "method".to_string(),
                            Value::String("BboTickers".to_string()),
                        );
                        obj.insert("sync".to_string(), Value::Bool(true));
                        Self::add_extra(&mut obj, &args.extra)?;
                    }
                    SyncCommand::DepthExt(args) => {
                        obj.insert("method".to_string(), Value::String("Depth".to_string()));
                        obj.insert("sync".to_string(), Value::Bool(true));
                        obj.insert("symbol".to_string(), serde_json::to_value(&args.symbol)?);
                        obj.insert("limit".to_string(), serde_json::to_value(args.limit)?);
                        Self::add_extra(&mut obj, &args.extra)?;
                    }
                    SyncCommand::InstrumentExt(args) => {
                        obj.insert(
                            "method".to_string(),
                            Value::String("Instrument".to_string()),
                        );
                        obj.insert("sync".to_string(), Value::Bool(true));
                        obj.insert("symbol".to_string(), serde_json::to_value(&args.symbol)?);
                        Self::add_extra(&mut obj, &args.extra)?;
                    }
                    SyncCommand::InstrumentsExt(args) => {
                        obj.insert(
                            "method".to_string(),
                            Value::String("Instruments".to_string()),
                        );
                        obj.insert("sync".to_string(), Value::Bool(true));
                        Self::add_extra(&mut obj, &args.extra)?;
                    }
                    SyncCommand::FundingRateBySymbolExt(args) => {
                        obj.insert(
                            "method".to_string(),
                            Value::String("FundingRateBySymbol".to_string()),
                        );
                        obj.insert("sync".to_string(), Value::Bool(true));
                        obj.insert("symbol".to_string(), serde_json::to_value(&args.symbol)?);
                        Self::add_extra(&mut obj, &args.extra)?;
                    }
                    SyncCommand::FundingRateExt(args) => {
                        obj.insert(
                            "method".to_string(),
                            Value::String("FundingRate".to_string()),
                        );
                        obj.insert("sync".to_string(), Value::Bool(true));
                        Self::add_extra(&mut obj, &args.extra)?;
                    }
                    SyncCommand::MarkPriceExt(args) => {
                        obj.insert("method".to_string(), Value::String("MarkPrice".to_string()));
                        obj.insert("sync".to_string(), Value::Bool(true));
                        obj.insert("symbol".to_string(), serde_json::to_value(&args.symbol)?);
                        Self::add_extra(&mut obj, &args.extra)?;
                    }
                    SyncCommand::KlineExt(args) => {
                        obj.insert("method".to_string(), Value::String("Kline".to_string()));
                        obj.insert("sync".to_string(), Value::Bool(true));
                        obj.insert("symbol".to_string(), serde_json::to_value(&args.symbol)?);
                        obj.insert(
                            "interval".to_string(),
                            serde_json::to_value(&args.interval)?,
                        );
                        if let Some(start_time) = args.start_time {
                            obj.insert("start_time".to_string(), serde_json::to_value(start_time)?);
                        }
                        if let Some(end_time) = args.end_time {
                            obj.insert("end_time".to_string(), serde_json::to_value(end_time)?);
                        }
                        if let Some(limit) = args.limit {
                            obj.insert("limit".to_string(), serde_json::to_value(limit)?);
                        }
                        Self::add_extra(&mut obj, &args.extra)?;
                    }
                    SyncCommand::FundingRateHistory(args) => {
                        obj.insert(
                            "method".to_string(),
                            Value::String("FundingRateHistory".to_string()),
                        );
                        obj.insert("sync".to_string(), Value::Bool(true));
                        if let Some(symbol) = &args.symbol {
                            obj.insert("symbol".to_string(), serde_json::to_value(symbol)?);
                        }
                        if let Some(since_secs) = args.since_secs {
                            obj.insert("since_secs".to_string(), serde_json::to_value(since_secs)?);
                        }
                        obj.insert("limit".to_string(), serde_json::to_value(args.limit)?);
                        Self::add_extra(&mut obj, &args.extra)?;
                    }
                    SyncCommand::Dex(value) => {
                        obj.insert("method".to_string(), Value::String("Dex".to_string()));
                        obj.insert("sync".to_string(), Value::Bool(true));
                        obj.insert("value".to_string(), serde_json::to_value(value)?);
                    }
                    // 其他同步命令不支持
                    _ => return Err(qerror!("不支持的命令类型: {:?}", sync_cmd)),
                }
            }
            Command::Async(async_cmd) => match async_cmd {
                AsyncCommand::PlaceOrder(args) => {
                    obj.insert(
                        "method".to_string(),
                        Value::String("PlaceOrder".to_string()),
                    );
                    obj.insert("sync".to_string(), Value::Bool(false));
                    obj.insert("order".to_string(), serde_json::to_value(&args.0)?);
                    obj.insert("params".to_string(), serde_json::to_value(&args.1)?);
                }
                AsyncCommand::PlaceOrderExt(args) => {
                    obj.insert(
                        "method".to_string(),
                        Value::String("PlaceOrder".to_string()),
                    );
                    obj.insert("sync".to_string(), Value::Bool(false));
                    obj.insert("order".to_string(), serde_json::to_value(&args.order)?);
                    obj.insert("params".to_string(), serde_json::to_value(&args.params)?);
                    Self::add_extra(&mut obj, &args.extra)?;
                }
                AsyncCommand::BatchPlaceOrder(args) => {
                    obj.insert(
                        "method".to_string(),
                        Value::String("BatchPlaceOrder".to_string()),
                    );
                    obj.insert("sync".to_string(), Value::Bool(false));
                    obj.insert("orders".to_string(), serde_json::to_value(&args.0)?);
                    obj.insert("params".to_string(), serde_json::to_value(&args.1)?);
                }
                AsyncCommand::BatchPlaceOrderExt(args) => {
                    obj.insert(
                        "method".to_string(),
                        Value::String("BatchPlaceOrder".to_string()),
                    );
                    obj.insert("sync".to_string(), Value::Bool(false));
                    obj.insert("orders".to_string(), serde_json::to_value(&args.orders)?);
                    obj.insert("params".to_string(), serde_json::to_value(&args.params)?);
                    Self::add_extra(&mut obj, &args.extra)?;
                }
                AsyncCommand::AmendOrder(order) => {
                    obj.insert(
                        "method".to_string(),
                        Value::String("AmendOrder".to_string()),
                    );
                    obj.insert("sync".to_string(), Value::Bool(false));
                    obj.insert("order".to_string(), serde_json::to_value(order)?);
                }
                AsyncCommand::AmendOrderExt(args) => {
                    obj.insert(
                        "method".to_string(),
                        Value::String("AmendOrder".to_string()),
                    );
                    obj.insert("sync".to_string(), Value::Bool(false));
                    obj.insert("order".to_string(), serde_json::to_value(&args.order)?);
                    Self::add_extra(&mut obj, &args.extra)?;
                }
                AsyncCommand::CancelOrder(args) => {
                    obj.insert(
                        "method".to_string(),
                        Value::String("CancelOrder".to_string()),
                    );
                    obj.insert("sync".to_string(), Value::Bool(false));
                    obj.insert("order_id".to_string(), serde_json::to_value(&args.0)?);
                    obj.insert("symbol".to_string(), serde_json::to_value(&args.1)?);
                }
                AsyncCommand::CancelOrderExt(args) => {
                    obj.insert(
                        "method".to_string(),
                        Value::String("CancelOrder".to_string()),
                    );
                    obj.insert("sync".to_string(), Value::Bool(false));
                    obj.insert("symbol".to_string(), serde_json::to_value(&args.symbol)?);
                    obj.insert(
                        "order_id".to_string(),
                        serde_json::to_value(&args.order_id)?,
                    );
                    Self::add_extra(&mut obj, &args.extra)?;
                }
                AsyncCommand::BatchCancelOrder(symbol) => {
                    obj.insert(
                        "method".to_string(),
                        Value::String("BatchCancelOrder".to_string()),
                    );
                    obj.insert("sync".to_string(), Value::Bool(false));
                    obj.insert("symbol".to_string(), serde_json::to_value(symbol)?);
                }
                AsyncCommand::BatchCancelOrderExt(args) => {
                    obj.insert(
                        "method".to_string(),
                        Value::String("BatchCancelOrder".to_string()),
                    );
                    obj.insert("sync".to_string(), Value::Bool(false));
                    obj.insert("symbol".to_string(), serde_json::to_value(&args.symbol)?);
                    Self::add_extra(&mut obj, &args.extra)?;
                }
                AsyncCommand::BatchCancelOrderById((symbol, ids, cids)) => {
                    obj.insert(
                        "method".to_string(),
                        Value::String("BatchCancelOrderById".to_string()),
                    );
                    obj.insert("sync".to_string(), Value::Bool(false));
                    obj.insert("symbol".to_string(), serde_json::to_value(symbol)?);
                    obj.insert("ids".to_string(), serde_json::to_value(ids)?);
                    obj.insert("cids".to_string(), serde_json::to_value(cids)?);
                }
                AsyncCommand::BatchCancelOrderByIdExt(args) => {
                    obj.insert(
                        "method".to_string(),
                        Value::String("BatchCancelOrderById".to_string()),
                    );
                    obj.insert("sync".to_string(), Value::Bool(false));
                    obj.insert("symbol".to_string(), serde_json::to_value(&args.symbol)?);
                    obj.insert("ids".to_string(), serde_json::to_value(&args.ids)?);
                    obj.insert("cids".to_string(), serde_json::to_value(&args.cids)?);
                    Self::add_extra(&mut obj, &args.extra)?;
                }
                AsyncCommand::Dex(value) => {
                    obj.insert("method".to_string(), Value::String("Dex".to_string()));
                    obj.insert("sync".to_string(), Value::Bool(false));
                    obj.insert("value".to_string(), serde_json::to_value(value)?);
                }
            },
        }

        Ok(Value::Object(obj))
    }

    /// 将扁平化格式的JSON解析为ExecutionCommand
    pub fn from_flatten(value: Value) -> Result<Self> {
        // 确保输入是对象
        let obj = match value {
            Value::Object(obj) => obj,
            _ => return Err(qerror!("命令必须是对象, 命令: {:?}", value)),
        };

        // 提取基本字段
        let method = obj
            .get("method")
            .ok_or_else(|| qerror!("缺少method字段"))?
            .as_str()
            .ok_or_else(|| qerror!("method必须是字符串"))?
            .to_string();

        // 使用辅助函数获取sync字段，不存在则为false
        let is_sync = Self::get_sync_or_false(&obj);

        let account_id: AccountId =
            obj.get("account_id")
                .ok_or_else(|| qerror!("缺少account_id字段"))?
                .as_u64()
                .ok_or_else(|| qerror!("account_id必须是整数"))? as AccountId;

        // 创建默认上下文
        let context = Context::default();

        // 根据method和is_sync解析命令
        let cmd = if is_sync {
            Self::parse_sync_flatten_command(&method, &obj)?
        } else {
            Self::parse_async_flatten_command(&method, &obj)?
        };

        Ok(Self {
            account_id,
            context,
            cmd,
        })
    }

    /// 解析扁平化格式中的同步命令
    #[inline]
    fn parse_sync_flatten_command(
        method: &str,
        obj: &serde_json::Map<String, Value>,
    ) -> Result<Command> {
        // 根据方法名解析不同类型的命令
        let sync_cmd = match method {
            "Request" => {
                let req = obj
                    .get("request")
                    .ok_or_else(|| qerror!("Request命令缺少request字段"))?;
                let user_request: UserRequest = serde_json::from_value(req.clone())
                    .map_err(|e| qerror!("解析Request参数失败: {}", e))?;

                SyncCommand::Request(user_request)
            }
            "GetOrders" => {
                let symbol = Self::get_symbol(obj)?;

                let start_time = obj
                    .get("start_time")
                    .ok_or_else(|| qerror!("GetOrders命令缺少start_time字段"))?;
                let start_time: i64 = serde_json::from_value(start_time.clone())
                    .map_err(|e| qerror!("解析GetOrders start_time参数失败: {}", e))?;

                let end_time = obj
                    .get("end_time")
                    .ok_or_else(|| qerror!("GetOrders命令缺少end_time字段"))?;
                let end_time: i64 = serde_json::from_value(end_time.clone())
                    .map_err(|e| qerror!("解析GetOrders end_time参数失败: {}", e))?;

                let extra = Self::get_extra_or_empty(obj)?;

                SyncCommand::GetOrdersExt(GetOrdersParams {
                    symbol,
                    start_time,
                    end_time,
                    extra,
                })
            }
            "GetOpenOrders" => {
                let symbol = Self::get_symbol(obj)?;
                let extra = Self::get_extra_or_empty(obj)?;

                SyncCommand::GetOpenOrdersExt(GetOpenOrdersParams { symbol, extra })
            }
            "GetAllOpenOrders" => {
                let extra = Self::get_extra_or_empty(obj)?;

                SyncCommand::GetAllOpenOrdersExt(GetAllOpenOrdersParams { extra })
            }
            "GetOrderById" => {
                let symbol = Self::get_symbol(obj)?;

                let order_id = obj
                    .get("order_id")
                    .ok_or_else(|| qerror!("GetOrderById命令缺少order_id字段"))?;
                let order_id: OrderId = serde_json::from_value(order_id.clone())
                    .map_err(|e| qerror!("解析GetOrderById order_id参数失败: {}", e))?;

                let extra = Self::get_extra_or_empty(obj)?;

                SyncCommand::GetOrderByIdExt(GetOrderByIdParams {
                    symbol,
                    order_id,
                    extra,
                })
            }
            "PlaceOrder" => {
                // 从对象中提取字段
                let order = obj
                    .get("order")
                    .ok_or_else(|| qerror!("PlaceOrder命令缺少order字段"))?;
                let order: Order = serde_json::from_value(order.clone())
                    .map_err(|e| qerror!("解析PlaceOrder order参数失败: {}", e))?;

                let params = obj
                    .get("params")
                    .ok_or_else(|| qerror!("PlaceOrder命令缺少params字段"))?;
                let params: OrderParams = serde_json::from_value(params.clone())
                    .map_err(|e| qerror!("解析PlaceOrder params参数失败: {}", e))?;

                let extra = Self::get_extra_or_empty(obj)?;

                let args = PostOrderParams {
                    order,
                    params,
                    extra,
                };

                SyncCommand::PlaceOrderExt(args)
            }
            "BatchPlaceOrder" => {
                let orders = obj
                    .get("orders")
                    .ok_or_else(|| qerror!("BatchPlaceOrder命令缺少orders字段"))?;
                let orders: Vec<Order> = serde_json::from_value(orders.clone())
                    .map_err(|e| qerror!("解析BatchPlaceOrder orders参数失败: {}", e))?;

                let params = obj
                    .get("params")
                    .ok_or_else(|| qerror!("BatchPlaceOrder命令缺少params字段"))?;
                let params: OrderParams = serde_json::from_value(params.clone())
                    .map_err(|e| qerror!("解析BatchPlaceOrder params参数失败: {}", e))?;

                let extra = Self::get_extra_or_empty(obj)?;

                let args = PostBatchOrderParams {
                    orders,
                    params,
                    extra,
                };

                SyncCommand::BatchPlaceOrderExt(args)
            }
            "AmendOrder" => {
                let order = obj
                    .get("order")
                    .ok_or_else(|| qerror!("AmendOrder命令缺少order字段"))?;
                let order: Order = serde_json::from_value(order.clone())
                    .map_err(|e| qerror!("解析AmendOrder order参数失败: {}", e))?;

                let extra = Self::get_extra_or_empty(obj)?;

                let args = AmendOrderParams { order, extra };

                SyncCommand::AmendOrderExt(args)
            }
            "CancelOrder" => {
                let symbol = obj
                    .get("symbol")
                    .ok_or_else(|| qerror!("CancelOrder命令缺少symbol字段"))?;
                let symbol: Symbol = serde_json::from_value(symbol.clone())
                    .map_err(|e| qerror!("解析CancelOrder symbol参数失败: {}", e))?;

                let order_id = obj
                    .get("order_id")
                    .ok_or_else(|| qerror!("CancelOrder命令缺少order_id字段"))?;
                let order_id: OrderId = serde_json::from_value(order_id.clone())
                    .map_err(|e| qerror!("解析CancelOrder order_id参数失败: {}", e))?;

                let extra = Self::get_extra_or_empty(obj)?;

                let args = CancelOrderParams {
                    symbol,
                    order_id,
                    extra,
                };

                SyncCommand::CancelOrderExt(args)
            }
            "BatchCancelOrder" => {
                let symbol = obj
                    .get("symbol")
                    .ok_or_else(|| qerror!("BatchCancelOrder命令缺少symbol字段"))?;
                let symbol: Symbol = serde_json::from_value(symbol.clone())
                    .map_err(|e| qerror!("解析BatchCancelOrder symbol参数失败: {}", e))?;

                let extra = Self::get_extra_or_empty(obj)?;

                let args = BatchCancelOrderParams { symbol, extra };

                SyncCommand::BatchCancelOrderExt(args)
            }
            "BatchCancelOrderById" => {
                let symbol = Self::get_opt_symbol(obj)?;
                let ids = Self::get_opt_string_array(obj, "ids")?;
                let cids = Self::get_opt_string_array(obj, "cids")?;
                let extra = Self::get_extra_or_empty(obj)?;

                SyncCommand::BatchCancelOrderByIdExt(BatchCancelOrderByIdsParams {
                    symbol,
                    ids,
                    cids,
                    extra,
                })
            }
            "Position" => {
                let symbol = Self::get_symbol(obj)?;

                let extra = Self::get_extra_or_empty(obj)?;

                SyncCommand::PositionExt(GetPositionParams { symbol, extra })
            }
            "Positions" => {
                let extra = Self::get_extra_or_empty(obj)?;

                SyncCommand::PositionsExt(GetPositionsParams { extra })
            }
            "MaxPosition" => {
                let symbol = Self::get_symbol(obj)?;

                let extra = Self::get_extra_or_empty(obj)?;

                // 默认杠杆为0，可以根据需要从参数中提取
                let leverage = 0;

                SyncCommand::MaxPositionExt(GetMaxPositionParams {
                    symbol,
                    extra,
                    leverage,
                })
            }
            "FundingFee" => {
                let symbol = Self::get_symbol(obj)?;

                let extra = Self::get_extra_or_empty(obj)?;

                let args = GetFundingFeeParams {
                    symbol,
                    start_time: None,
                    end_time: None,
                    extra,
                };

                SyncCommand::FundingFeeExt(args)
            }
            "MaxLeverage" => {
                let symbol = Self::get_symbol(obj)?;

                let extra = Self::get_extra_or_empty(obj)?;

                let args = GetMaxLeverageParams { symbol, extra };

                SyncCommand::MaxLeverageExt(args)
            }
            "MarginMode" => {
                let symbol = Self::get_symbol(obj)?;

                let extra = Self::get_extra_or_empty(obj)?;

                let args = GetMarginModeParams {
                    symbol,
                    margin_coin: "".to_string(), // 默认空值
                    extra,
                };

                SyncCommand::MarginModeExt(args)
            }
            "SetMarginMode" => {
                let symbol = Self::get_symbol(obj)?;

                let margin_mode = obj
                    .get("margin_mode")
                    .ok_or_else(|| qerror!("SetMarginMode命令缺少margin_mode字段"))?;
                let margin_mode = serde_json::from_value(margin_mode.clone())
                    .map_err(|e| qerror!("解析SetMarginMode margin_mode参数失败: {}", e))?;

                let margin_coin = obj
                    .get("margin_coin")
                    .ok_or_else(|| qerror!("SetMarginMode命令缺少margin_coin字段"))?;
                let margin_coin = serde_json::from_value(margin_coin.clone())
                    .map_err(|e| qerror!("解析SetMarginMode margin_coin参数失败: {}", e))?;

                let extra = Self::get_extra_or_empty(obj)?;

                let args = SetMarginModeParams {
                    symbol,
                    margin_mode,
                    margin_coin,
                    extra,
                };

                SyncCommand::SetMarginModeExt(args)
            }
            "UsdtBalance" => {
                let extra = Self::get_extra_or_empty(obj)?;

                let args = GetUsdtBalanceParams { extra };

                SyncCommand::UsdtBalanceExt(args)
            }
            "Balance" => {
                let extra = Self::get_extra_or_empty(obj)?;

                let args = GetBalancesParams { extra };

                SyncCommand::BalanceExt(args)
            }
            "BalanceByCoin" => {
                let asset = obj
                    .get("asset")
                    .ok_or_else(|| qerror!("BalanceByCoin命令缺少asset字段"))?;
                let asset: String = serde_json::from_value(asset.clone())
                    .map_err(|e| qerror!("解析BalanceByCoin asset参数失败: {}", e))?;

                let extra = Self::get_extra_or_empty(obj)?;

                let args = GetBalanceParams { asset, extra };

                SyncCommand::BalanceByCoinExt(args)
            }
            "FeeRate" => {
                let symbol = Self::get_symbol(obj)?;

                let extra = Self::get_extra_or_empty(obj)?;

                let args = GetFeeRateParams { symbol, extra };

                SyncCommand::FeeRateExt(args)
            }
            "SetLeverage" => {
                let symbol = Self::get_symbol(obj)?;

                let leverage = obj
                    .get("leverage")
                    .ok_or_else(|| qerror!("SetLeverage命令缺少leverage字段"))?;
                let leverage = serde_json::from_value(leverage.clone())
                    .map_err(|e| qerror!("解析SetLeverage leverage参数失败: {}", e))?;

                let extra = Self::get_extra_or_empty(obj)?;

                let args = SetLeverageParams {
                    symbol,
                    leverage,
                    extra,
                };

                SyncCommand::SetLeverageExt(args)
            }
            "IsDualSidePosition" => {
                let extra = Self::get_extra_or_empty(obj)?;

                let args = IsDualSideParams { extra };

                SyncCommand::IsDualSidePositionExt(args)
            }
            "SetDualSidePosition" => {
                let is_dual_side = obj
                    .get("is_dual_side")
                    .ok_or_else(|| qerror!("SetDualSidePosition命令缺少is_dual_side字段"))?;
                let is_dual_side: bool = serde_json::from_value(is_dual_side.clone())
                    .map_err(|e| qerror!("解析SetDualSidePosition is_dual_side参数失败: {}", e))?;

                let extra = Self::get_extra_or_empty(obj)?;

                let args = SetDualSideParams {
                    is_dual_side,
                    extra,
                };

                SyncCommand::SetDualSidePositionExt(args)
            }
            "Transfer" => {
                let transfer = obj
                    .get("transfer")
                    .ok_or_else(|| qerror!("Transfer命令缺少transfer字段"))?;
                let transfer = serde_json::from_value(transfer.clone())
                    .map_err(|e| qerror!("解析Transfer transfer参数失败: {}", e))?;

                let extra = Self::get_extra_or_empty(obj)?;

                let args = TransferParams { transfer, extra };

                SyncCommand::TransferExt(args)
            }
            "SubTransfer" => {
                let transfer = obj
                    .get("transfer")
                    .ok_or_else(|| qerror!("SubTransfer命令缺少transfer字段"))?;
                let transfer = serde_json::from_value(transfer.clone())
                    .map_err(|e| qerror!("解析SubTransfer transfer参数失败: {}", e))?;

                let extra = Self::get_extra_or_empty(obj)?;

                let args = SubTransferParams { transfer, extra };

                SyncCommand::SubTransferExt(args)
            }
            "GetDepositAddress" => {
                let ccy = obj
                    .get("ccy")
                    .ok_or_else(|| qerror!("GetDepositAddress命令缺少ccy字段"))?;
                let ccy: String = serde_json::from_value(ccy.clone())
                    .map_err(|e| qerror!("解析GetDepositAddress ccy参数失败: {}", e))?;

                let chain = obj
                    .get("chain")
                    .ok_or_else(|| qerror!("GetDepositAddress命令缺少chain字段"))?;
                let chain = serde_json::from_value(chain.clone())
                    .map_err(|e| qerror!("解析GetDepositAddress chain参数失败: {}", e))?;

                let amount = obj
                    .get("amount")
                    .ok_or_else(|| qerror!("GetDepositAddress命令缺少amount字段"))?;
                let amount = serde_json::from_value(amount.clone())
                    .map_err(|e| qerror!("解析GetDepositAddress amount参数失败: {}", e))?;

                let extra = Self::get_extra_or_empty(obj)?;

                let args = GetDepositAddressParams {
                    ccy,
                    chain,
                    amount,
                    extra,
                };

                SyncCommand::GetDepositAddressExt(args)
            }
            "Withdrawal" => {
                let params = obj
                    .get("params")
                    .ok_or_else(|| qerror!("Withdrawal命令缺少params字段"))?;
                let params = serde_json::from_value(params.clone())
                    .map_err(|e| qerror!("解析Withdrawal params参数失败: {}", e))?;

                let extra = Self::get_extra_or_empty(obj)?;

                let args = WithdrawalParams { params, extra };

                SyncCommand::WithdrawalExt(args)
            }
            "GetAccountInfo" => {
                let extra = Self::get_extra_or_empty(obj)?;

                let args = GetAccountInfoParams { extra };

                SyncCommand::GetAccountInfoExt(args)
            }
            "GetAccountMode" => {
                let extra = Self::get_extra_or_empty(obj)?;

                let args = GetAccountModeParams { extra };

                SyncCommand::GetAccountModeExt(args)
            }
            "SetAccountMode" => {
                let mode = obj
                    .get("mode")
                    .ok_or_else(|| qerror!("SetAccountMode命令缺少mode字段"))?;
                let mode = serde_json::from_value(mode.clone())
                    .map_err(|e| qerror!("解析SetAccountMode mode参数失败: {}", e))?;

                let extra = Self::get_extra_or_empty(obj)?;

                let args = SetAccountModeParams { mode, extra };

                SyncCommand::SetAccountModeExt(args)
            }
            "GetUserId" => {
                let extra = Self::get_extra_or_empty(obj)?;

                let args = GetUserIdParams { extra };

                SyncCommand::GetUserIdExt(args)
            }
            "GetFeeDiscountInfo" => {
                let extra = Self::get_extra_or_empty(obj)?;

                let args = GetFeeDiscountInfoParams { extra };

                SyncCommand::GetFeeDiscountInfoExt(args)
            }
            "IsFeeDiscountEnabled" => {
                let extra = Self::get_extra_or_empty(obj)?;

                let args = IsFeeDiscountEnabledParams { extra };

                SyncCommand::IsFeeDiscountEnabledExt(args)
            }
            "SetFeeDiscountEnabled" => {
                let enable = obj
                    .get("enable")
                    .ok_or_else(|| qerror!("SetFeeDiscountEnabled命令缺少enable字段"))?;
                let enable: bool = serde_json::from_value(enable.clone())
                    .map_err(|e| qerror!("解析SetFeeDiscountEnabled enable参数失败: {}", e))?;

                let extra = Self::get_extra_or_empty(obj)?;

                let args = SetFeeDiscountEnabledParams { enable, extra };

                SyncCommand::SetFeeDiscountEnabledExt(args)
            }
            "Borrow" => {
                let coin = obj
                    .get("coin")
                    .ok_or_else(|| qerror!("Borrow命令缺少coin字段"))?;
                let coin: String = serde_json::from_value(coin.clone())
                    .map_err(|e| qerror!("解析Borrow coin参数失败: {}", e))?;

                let amount = obj
                    .get("amount")
                    .ok_or_else(|| qerror!("Borrow命令缺少amount字段"))?;
                let amount: f64 = serde_json::from_value(amount.clone())
                    .map_err(|e| qerror!("解析Borrow amount参数失败: {}", e))?;

                let extra = Self::get_extra_or_empty(obj)?;

                let args = BorrowCoinParams {
                    coin,
                    amount,
                    extra,
                };

                SyncCommand::BorrowExt(args)
            }
            "GetBorrowed" => {
                let coin = obj.get("coin");
                let coin = if let Some(coin_val) = coin {
                    serde_json::from_value(coin_val.clone())
                        .map_err(|e| qerror!("解析GetBorrowed coin参数失败: {}", e))?
                } else {
                    None
                };

                let extra = Self::get_extra_or_empty(obj)?;

                let args = GetBorrowParams { coin, extra };

                SyncCommand::GetBorrowedExt(args)
            }
            "Repay" => {
                let coin = obj
                    .get("coin")
                    .ok_or_else(|| qerror!("Repay命令缺少coin字段"))?;
                let coin: String = serde_json::from_value(coin.clone())
                    .map_err(|e| qerror!("解析Repay coin参数失败: {}", e))?;

                let amount = obj
                    .get("amount")
                    .ok_or_else(|| qerror!("Repay命令缺少amount字段"))?;
                let amount: f64 = serde_json::from_value(amount.clone())
                    .map_err(|e| qerror!("解析Repay amount参数失败: {}", e))?;

                let extra = Self::get_extra_or_empty(obj)?;

                let args = RepayCoinParams {
                    coin,
                    amount,
                    extra,
                };

                SyncCommand::RepayExt(args)
            }
            "GetBorrowRate" => {
                let coin = obj.get("coin");
                let coin = if let Some(coin_val) = coin {
                    serde_json::from_value(coin_val.clone())
                        .map_err(|e| qerror!("解析GetBorrowRate coin参数失败: {}", e))?
                } else {
                    None
                };

                let extra = Self::get_extra_or_empty(obj)?;

                let args = GetBorrowRateParams { coin, extra };

                SyncCommand::GetBorrowRateExt(args)
            }
            "GetBorrowLimit" => {
                let coin = obj
                    .get("coin")
                    .ok_or_else(|| qerror!("GetBorrowLimit命令缺少coin字段"))?;
                let coin: String = serde_json::from_value(coin.clone())
                    .map_err(|e| qerror!("解析GetBorrowLimit coin参数失败: {}", e))?;

                let extra = Self::get_extra_or_empty(obj)?;

                let args = GetBorrowLimitsParams {
                    coin,
                    extra,
                    is_vip: Some(false), // 默认值
                };

                SyncCommand::GetBorrowLimitExt(args)
            }
            "Ticker" => {
                let symbol = Self::get_symbol(obj)?;

                let extra = Self::get_extra_or_empty(obj)?;

                let args = GetTickerParams { symbol, extra };

                SyncCommand::TickerExt(args)
            }
            "Tickers" => {
                let extra = Self::get_extra_or_empty(obj)?;

                let args = GetTickersParams { extra };

                SyncCommand::TickersExt(args)
            }
            "Bbo" => {
                let symbol = Self::get_symbol(obj)?;

                let extra = Self::get_extra_or_empty(obj)?;

                let args = GetBboTickerParams { symbol, extra };

                SyncCommand::BboExt(args)
            }
            "BboTickers" => {
                let extra = Self::get_extra_or_empty(obj)?;

                let args = GetBboTickersParams { extra };

                SyncCommand::BboTickersExt(args)
            }
            "Depth" => {
                let symbol = Self::get_symbol(obj)?;

                let limit = obj
                    .get("limit")
                    .ok_or_else(|| qerror!("Depth命令缺少limit字段"))?;
                let limit = serde_json::from_value(limit.clone())
                    .map_err(|e| qerror!("解析Depth limit参数失败: {}", e))?;

                let extra = Self::get_extra_or_empty(obj)?;

                let args = GetDepthParams {
                    symbol,
                    limit,
                    extra,
                };

                SyncCommand::DepthExt(args)
            }
            "Instrument" => {
                let symbol = Self::get_symbol(obj)?;

                let extra = Self::get_extra_or_empty(obj)?;

                let args = GetInstrumentParams { symbol, extra };

                SyncCommand::InstrumentExt(args)
            }
            "Instruments" => {
                let extra = Self::get_extra_or_empty(obj)?;

                let args = GetInstrumentsParams { extra };

                SyncCommand::InstrumentsExt(args)
            }
            "FundingRateBySymbol" => {
                let symbol = Self::get_symbol(obj)?;

                let extra = Self::get_extra_or_empty(obj)?;

                let args = GetFundingRateParams { symbol, extra };

                SyncCommand::FundingRateBySymbolExt(args)
            }
            "FundingRate" => {
                let extra = Self::get_extra_or_empty(obj)?;

                let args = GetFundingRatesParams { extra };

                SyncCommand::FundingRateExt(args)
            }
            "MarkPrice" => {
                let symbol = Self::get_symbol(obj)?;

                let extra = Self::get_extra_or_empty(obj)?;

                let args = GetMarkPriceParams {
                    symbol: Some(symbol),
                    extra,
                };

                SyncCommand::MarkPriceExt(args)
            }
            "Kline" => {
                let symbol = Self::get_symbol(obj)?;

                let interval = obj
                    .get("interval")
                    .ok_or_else(|| qerror!("Kline命令缺少interval字段"))?;
                let interval = serde_json::from_value(interval.clone())
                    .map_err(|e| qerror!("解析Kline interval参数失败: {}", e))?;

                let start_time = obj
                    .get("start_time")
                    .map(|v| serde_json::from_value(v.clone()))
                    .transpose()
                    .map_err(|e| qerror!("解析Kline start_time参数失败: {}", e))?;

                let end_time = obj
                    .get("end_time")
                    .map(|v| serde_json::from_value(v.clone()))
                    .transpose()
                    .map_err(|e| qerror!("解析Kline end_time参数失败: {}", e))?;

                let limit = obj
                    .get("limit")
                    .map(|v| serde_json::from_value(v.clone()))
                    .transpose()
                    .map_err(|e| qerror!("解析Kline limit参数失败: {}", e))?;

                let extra = Self::get_extra_or_empty(obj)?;

                let args = GetKlineParams {
                    symbol,
                    interval,
                    start_time,
                    end_time,
                    limit,
                    extra,
                };

                SyncCommand::KlineExt(args)
            }
            "FundingRateHistory" => {
                let symbol = Self::get_opt_symbol(obj)?;

                let since_secs = obj
                    .get("since_secs")
                    .map(|v| serde_json::from_value(v.clone()))
                    .transpose()
                    .map_err(|e| qerror!("解析FundingRateHistory since_secs参数失败: {}", e))?;

                let limit = obj
                    .get("limit")
                    .ok_or_else(|| qerror!("FundingRateHistory命令缺少limit字段"))?;
                let limit: u32 = serde_json::from_value(limit.clone())
                    .map_err(|e| qerror!("解析FundingRateHistory limit参数失败: {}", e))?;

                let extra = Self::get_extra_or_empty(obj)?;

                let args = GetFundingRateHistoryParams {
                    symbol,
                    since_secs,
                    limit,
                    extra,
                };

                SyncCommand::FundingRateHistory(args)
            }
            "Dex" => {
                let value = obj
                    .get("value")
                    .ok_or_else(|| qerror!("Dex命令缺少value字段"))?
                    .clone();

                SyncCommand::Dex(value)
            }
            // 添加更多命令的解析...
            _ => return Err(qerror!("未知的同步命令: {}", method)),
        };

        Ok(Command::Sync(sync_cmd))
    }

    /// 解析扁平化格式中的异步命令
    #[inline]
    fn parse_async_flatten_command(
        method: &str,
        obj: &serde_json::Map<String, Value>,
    ) -> Result<Command> {
        // 现有的匹配逻辑
        let async_cmd = match method {
            "PlaceOrder" => {
                // 从对象中提取字段
                let order = obj
                    .get("order")
                    .ok_or_else(|| qerror!("PlaceOrder异步命令缺少order字段"))?;
                let order: Order = serde_json::from_value(order.clone())
                    .map_err(|e| qerror!("解析PlaceOrder order参数失败: {}", e))?;

                let params = obj
                    .get("params")
                    .ok_or_else(|| qerror!("PlaceOrder异步命令缺少params字段"))?;
                let params: OrderParams = serde_json::from_value(params.clone())
                    .map_err(|e| qerror!("解析PlaceOrder params参数失败: {}", e))?;

                let extra = Self::get_extra_or_empty(obj)?;

                AsyncCommand::PlaceOrderExt(PostOrderParams {
                    order,
                    params,
                    extra,
                })
            }
            "BatchPlaceOrder" => {
                let orders = obj
                    .get("orders")
                    .ok_or_else(|| qerror!("BatchPlaceOrder异步命令缺少orders字段"))?;
                let orders: Vec<Order> = serde_json::from_value(orders.clone())
                    .map_err(|e| qerror!("解析BatchPlaceOrder orders参数失败: {}", e))?;

                let params = obj
                    .get("params")
                    .ok_or_else(|| qerror!("BatchPlaceOrder异步命令缺少params字段"))?;
                let params: OrderParams = serde_json::from_value(params.clone())
                    .map_err(|e| qerror!("解析BatchPlaceOrder params参数失败: {}", e))?;

                let extra = Self::get_extra_or_empty(obj)?;

                AsyncCommand::BatchPlaceOrderExt(PostBatchOrderParams {
                    orders,
                    params,
                    extra,
                })
            }
            "AmendOrder" => {
                let order = obj
                    .get("order")
                    .ok_or_else(|| qerror!("AmendOrder异步命令缺少order字段"))?;
                let order: Order = serde_json::from_value(order.clone())
                    .map_err(|e| qerror!("解析AmendOrder order参数失败: {}", e))?;

                let extra = Self::get_extra_or_empty(obj)?;

                AsyncCommand::AmendOrderExt(AmendOrderParams { order, extra })
            }
            "CancelOrder" => {
                let symbol = Self::get_symbol(obj)?;
                let order_id = obj
                    .get("order_id")
                    .ok_or_else(|| qerror!("CancelOrder异步命令缺少order_id字段"))?;
                let order_id: OrderId = serde_json::from_value(order_id.clone())
                    .map_err(|e| qerror!("解析CancelOrder order_id参数失败: {}", e))?;

                let extra = Self::get_extra_or_empty(obj)?;

                AsyncCommand::CancelOrderExt(CancelOrderParams {
                    symbol,
                    order_id,
                    extra,
                })
            }
            "BatchCancelOrder" => {
                let symbol = Self::get_symbol(obj)?;
                let extra = Self::get_extra_or_empty(obj)?;

                AsyncCommand::BatchCancelOrderExt(BatchCancelOrderParams { symbol, extra })
            }
            "BatchCancelOrderById" => {
                let symbol = Self::get_opt_symbol(obj)?;
                let ids = Self::get_opt_string_array(obj, "ids")?;
                let cids = Self::get_opt_string_array(obj, "cids")?;
                let extra = Self::get_extra_or_empty(obj)?;

                AsyncCommand::BatchCancelOrderByIdExt(BatchCancelOrderByIdsParams {
                    symbol,
                    ids,
                    cids,
                    extra,
                })
            }
            "Dex" => {
                let value = obj
                    .get("value")
                    .ok_or_else(|| qerror!("Dex命令缺少value字段"))?
                    .clone();

                AsyncCommand::Dex(value)
            }
            // 其他异步命令不支持
            _ => return Err(qerror!("未知的异步命令: {:?}", method)),
        };

        Ok(Command::Async(async_cmd))
    }

    /// 将ExecutionCommand序列化为扁平化格式的JSON字符串
    pub fn to_flatten_string(&self) -> Result<String> {
        let obj = self.to_flatten()?;
        serde_json::to_string(&obj).map_err(|e| qerror!("序列化为JSON字符串失败: {}", e))
    }

    /// 将扁平化格式的JSON字符串解析为ExecutionCommand
    pub fn from_flatten_string(json_str: &str) -> Result<Self> {
        let value: Value =
            serde_json::from_str(json_str).map_err(|e| qerror!("解析JSON字符串失败: {}", e))?;
        Self::from_flatten(value)
    }

    /// 从Python返回值反序列化ExecutionCommand的v2版本
    /// 先将Python返回值反序列化为Value，然后调用ExecutionCommand的from_flatten方法
    pub fn from_python_return_v2(py_return: &str) -> Result<Self> {
        // 先将Python返回值解析为Value
        let value: Value = serde_json::from_str(py_return)
            .map_err(|e| qerror!("Python返回值解析为JSON失败: {}", e))?;

        // 调用ExecutionCommand的from_flatten方法
        Self::from_flatten(value)
    }

    /// 从Python返回值反序列化Vec<ExecutionCommand>的v2版本
    /// 用于处理cmds字段
    pub fn from_python_return_cmds_v2(py_return: &str) -> Result<Vec<Self>> {
        // 先将Python返回值解析为Value数组
        let values: Vec<Value> = serde_json::from_str(py_return)
            .map_err(|e| qerror!("Python返回值解析为JSON数组失败: {}", e))?;

        // 对每个Value调用from_flatten方法
        let mut cmds = Vec::with_capacity(values.len());
        for value in values {
            cmds.push(Self::from_flatten(value)?);
        }

        Ok(cmds)
    }

    /// 将ExecutionCommand序列化为美化后的扁平化格式JSON字符串，用于调试
    pub fn to_flatten_pretty(&self) -> Result<String> {
        let obj = self.to_flatten()?;
        serde_json::to_string_pretty(&obj).map_err(|e| qerror!("美化序列化失败: {}", e))
    }

    /// 解析Symbol字段
    #[inline]
    fn parse_symbol(symbol: &Value) -> Result<Symbol> {
        match symbol {
            Value::String(s) => serde_plain::from_str(s)
                .map_err(|e| qerror!("使用serde_plain解析symbol参数失败: {}", e)),
            Value::Object(_) => serde_json::from_value(symbol.clone())
                .map_err(|e| qerror!("解析symbol参数失败: {}", e)),
            _ => Err(qerror!("symbol格式不正确")),
        }
    }

    /// 获取Symbol字段
    #[inline]
    fn get_symbol(obj: &serde_json::Map<String, Value>) -> Result<Symbol> {
        let symbol = obj
            .get("symbol")
            .ok_or_else(|| qerror!("命令缺少symbol字段"))?;
        Self::parse_symbol(symbol)
    }

    /// 获取可选Symbol字段
    #[inline]
    fn get_opt_symbol(obj: &serde_json::Map<String, Value>) -> Result<Option<Symbol>> {
        if let Some(s) = obj.get("symbol") {
            match s {
                Value::String(s_str) => {
                    if s_str.is_empty() {
                        return Ok(None);
                    }
                    match serde_plain::from_str(s_str) {
                        Ok(symbol) => Ok(Some(symbol)),
                        Err(e) => Err(qerror!("使用serde_plain解析symbol参数失败: {}", e)),
                    }
                }
                Value::Object(_) => Ok(Some(
                    serde_json::from_value(s.clone())
                        .map_err(|e| qerror!("解析symbol参数失败: {}", e))?,
                )),
                Value::Null => Ok(None),
                _ => Err(qerror!("symbol格式不正确")),
            }
        } else {
            Ok(None)
        }
    }

    /// 获取可选字符串数组字段
    #[inline]
    fn get_opt_string_array(
        obj: &serde_json::Map<String, Value>,
        field: &str,
    ) -> Result<Option<Vec<String>>> {
        if let Some(val) = obj.get(field) {
            if val.is_null() {
                return Ok(None);
            }
            Ok(Some(
                serde_json::from_value(val.clone())
                    .map_err(|e| qerror!("解析{}参数失败: {}", field, e))?,
            ))
        } else {
            Ok(None)
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::model::context::Context;
    use quant_common::base::*;
    use std::collections::HashMap;

    #[test]
    fn test_place_order_serialize() {
        // 创建PlaceOrderExt命令
        let account_id: AccountId = 1;
        let order = Order {
            cid: Some("test1234567".to_string()),
            symbol: Symbol::new("BTC_USDT"),
            order_type: OrderType::Limit,
            side: OrderSide::Buy,
            pos_side: Some(PosSide::Long),
            price: Some(50000.0),
            amount: Some(0.1),
            time_in_force: TimeInForce::GTC,
            ..Order::default()
        };

        let params = OrderParams {
            is_dual_side: false,
            leverage: 10,
            margin_mode: Some(MarginMode::Cross),
            market_order_mode: MarketOrderMode::Normal,
            ..OrderParams::default()
        };

        let extra = HashMap::new();

        let cmd = ExecutionCommand {
            account_id,
            context: Context::default(),
            cmd: Command::Async(AsyncCommand::PlaceOrderExt(PostOrderParams {
                order,
                params,
                extra,
            })),
        };

        // 序列化为扁平化格式
        let flattened = cmd.to_flatten().unwrap();
        println!(
            "扁平化结果: {}",
            serde_json::to_string_pretty(&flattened).unwrap()
        );

        // 验证序列化结果
        let obj = flattened.as_object().unwrap();
        assert_eq!(obj.get("method").unwrap().as_str().unwrap(), "PlaceOrder");
        assert!(!obj.get("sync").unwrap().as_bool().unwrap());
        assert_eq!(obj.get("account_id").unwrap().as_u64().unwrap(), 1);

        // 验证order字段
        let order_obj = obj.get("order").unwrap().as_object().unwrap();
        assert_eq!(
            order_obj.get("cid").unwrap().as_str().unwrap(),
            "test1234567"
        );

        // 简化Symbol比较方式，仅检查字符串表示
        let symbol_str = format!("{}", order_obj.get("symbol").unwrap());
        assert!(symbol_str.contains("BTC") && symbol_str.contains("USDT"));

        // 验证params字段
        let params_obj = obj.get("params").unwrap().as_object().unwrap();
        assert_eq!(params_obj.get("leverage").unwrap().as_u64().unwrap(), 10);

        // 反序列化测试
        let deserialized = ExecutionCommand::from_flatten(flattened).unwrap();

        // 验证反序列化结果
        assert_eq!(deserialized.account_id, account_id);
        println!("deserialized: {deserialized:?}");
        match &deserialized.cmd {
            Command::Async(AsyncCommand::PlaceOrderExt(args)) => {
                assert_eq!(args.order.cid.as_ref().unwrap(), "test1234567");
                // 简化Symbol比较，只比较字符串表示
                assert!(
                    args.order.symbol.to_string().contains("BTC")
                        && args.order.symbol.to_string().contains("USDT")
                );
                assert_eq!(args.params.leverage, 10);
            }
            _ => panic!("反序列化后命令类型不匹配"),
        }
    }

    #[test]
    fn test_from_json_string() {
        // 测试从JSON字符串反序列化
        let json_str = r#"{
            "method": "PlaceOrder",
            "sync": false,
            "account_id": 1,
            "order": {
                "cid": "test1234567",
                "symbol": "BTC_USDT",
                "order_type": "Limit",
                "side": "Buy",
                "pos_side": "Long",
                "price": 50000.0,
                "amount": 0.1,
                "time_in_force": "GTC"
            },
            "params": {
                "is_dual_side": false,
                "leverage": 10,
                "margin_mode": "Cross",
                "market_order_mode": "Normal"
            },
            "extra": {}
        }"#;

        // 从JSON字符串反序列化
        let cmd = ExecutionCommand::from_flatten_string(json_str).unwrap();

        // 验证反序列化结果
        assert_eq!(cmd.account_id, 1);
        match &cmd.cmd {
            Command::Async(AsyncCommand::PlaceOrderExt(args)) => {
                assert_eq!(args.order.cid.as_ref().unwrap(), "test1234567");
                // 简化Symbol比较
                assert!(
                    args.order.symbol.to_string().contains("BTC")
                        && args.order.symbol.to_string().contains("USDT")
                );
                assert_eq!(args.params.leverage, 10);
            }
            _ => panic!("反序列化后命令类型不匹配"),
        }
    }

    #[test]
    fn test_cancel_order_flatten() {
        // 测试CancelOrder命令
        let account_id: AccountId = 1;
        let symbol = Symbol::new("BTC");
        let order_id = OrderId::Id("12345".to_string());
        let extra = HashMap::new();

        let args = CancelOrderParams {
            symbol: symbol.clone(),
            order_id: order_id.clone(),
            extra: extra.clone(),
        };

        let cmd = ExecutionCommand {
            account_id,
            context: Context::default(),
            cmd: Command::Async(AsyncCommand::CancelOrderExt(args)),
        };

        // 序列化为扁平化格式
        let flattened = cmd.to_flatten().unwrap();
        println!(
            "扁平化CancelOrder: {}",
            serde_json::to_string_pretty(&flattened).unwrap()
        );

        // 验证序列化结果
        let obj = flattened.as_object().unwrap();
        assert_eq!(obj.get("method").unwrap().as_str().unwrap(), "CancelOrder");
        assert!(!obj.get("sync").unwrap().as_bool().unwrap());

        // 反序列化
        let deserialized = ExecutionCommand::from_flatten(flattened).unwrap();

        // 验证反序列化结果
        match &deserialized.cmd {
            Command::Async(AsyncCommand::CancelOrderExt(args)) => {
                assert_eq!(args.symbol.to_string(), symbol.to_string());
                if let OrderId::Id(id_str) = &args.order_id {
                    assert_eq!(id_str, "12345");
                } else {
                    panic!("order_id类型不匹配");
                }
            }
            _ => panic!("反序列化后命令类型不匹配"),
        }
    }

    #[test]
    fn test_batch_cancel_order_by_id_flatten() {
        // 测试BatchCancelOrderById命令
        let account_id: AccountId = 1;
        let symbol = Some(Symbol::new("BTC_USDT"));
        let ids = Some(vec!["id1".to_string(), "id2".to_string()]);
        let cids = None;
        let extra = HashMap::new();

        let args = BatchCancelOrderByIdsParams {
            symbol: symbol.clone(),
            ids: ids.clone(),
            cids: cids.clone(),
            extra: extra.clone(),
        };

        let cmd = ExecutionCommand {
            account_id,
            context: Context::default(),
            cmd: Command::Async(AsyncCommand::BatchCancelOrderByIdExt(args)),
        };

        // 序列化为扁平化格式
        let flattened = cmd.to_flatten().unwrap();
        println!(
            "扁平化BatchCancelOrderById: {}",
            serde_json::to_string_pretty(&flattened).unwrap()
        );

        // 验证序列化结果
        let obj = flattened.as_object().unwrap();
        assert_eq!(
            obj.get("method").unwrap().as_str().unwrap(),
            "BatchCancelOrderById"
        );
        assert!(!obj.get("sync").unwrap().as_bool().unwrap());

        let ids_json = obj.get("ids").unwrap().as_array().unwrap();
        assert_eq!(ids_json.len(), 2);
        assert_eq!(ids_json[0].as_str().unwrap(), "id1");
        assert_eq!(ids_json[1].as_str().unwrap(), "id2");

        // 反序列化
        let deserialized = ExecutionCommand::from_flatten(flattened).unwrap();

        // 验证反序列化结果
        match &deserialized.cmd {
            Command::Async(AsyncCommand::BatchCancelOrderByIdExt(args)) => {
                if let Some(sym) = &args.symbol {
                    let _expected_str = "BTC_USDT";
                    // 只检查字符串表示中是否包含预期的部分，而不是精确匹配
                    let sym_str = sym.to_string();
                    assert!(
                        sym_str.contains("BTC") && sym_str.contains("USDT"),
                        "Symbol应该包含BTC和USDT，实际值: {sym_str}"
                    );
                } else {
                    panic!("symbol应该有值");
                }
                assert_eq!(args.ids.as_ref().unwrap().len(), 2);
                assert_eq!(args.ids.as_ref().unwrap()[0], "id1");
                assert!(args.cids.is_none());
            }
            _ => panic!("反序列化后命令类型不匹配"),
        }
    }

    #[test]
    fn test_position_flatten() {
        // 测试Position命令
        let account_id: AccountId = 1;
        let symbol = Symbol::new("BTC");
        let extra = HashMap::new();

        let args = GetPositionParams {
            symbol: symbol.clone(),
            extra: extra.clone(),
        };

        let cmd = ExecutionCommand {
            account_id,
            context: Context::default(),
            cmd: Command::Sync(SyncCommand::PositionExt(args)),
        };

        // 序列化为扁平化格式
        let flattened = cmd.to_flatten().unwrap();
        println!(
            "扁平化Position: {}",
            serde_json::to_string_pretty(&flattened).unwrap()
        );

        // 验证序列化结果
        let obj = flattened.as_object().unwrap();
        assert_eq!(obj.get("method").unwrap().as_str().unwrap(), "Position");
        assert!(obj.get("sync").unwrap().as_bool().unwrap());

        // 反序列化
        let deserialized = ExecutionCommand::from_flatten(flattened).unwrap();

        // 验证反序列化结果
        match &deserialized.cmd {
            Command::Sync(SyncCommand::PositionExt(args)) => {
                assert_eq!(args.symbol.to_string(), symbol.to_string());
            }
            _ => panic!("反序列化后命令类型不匹配"),
        }
    }

    #[test]
    fn test_flatten_cancel_order() {
        // 测试CancelOrder命令的扁平化
        let account_id: AccountId = 123;
        let symbol = Symbol::new("BTC");
        let order_id = OrderId::Id("12345".to_string());
        let extra = HashMap::new();

        // 创建CancelOrderParams
        let args = CancelOrderParams {
            symbol: symbol.clone(),
            order_id: order_id.clone(),
            extra: extra.clone(),
        };

        let cmd = ExecutionCommand {
            account_id,
            context: Context::default(),
            cmd: Command::Async(AsyncCommand::CancelOrderExt(args)),
        };

        // 序列化为扁平化格式
        let obj = cmd.to_flatten().unwrap();
        let json_str = serde_json::to_string_pretty(&obj).unwrap();
        println!("CancelOrder扁平化: {json_str}");

        // 验证序列化结果
        let json_obj = obj.as_object().unwrap();
        assert_eq!(
            json_obj.get("method").unwrap().as_str().unwrap(),
            "CancelOrder"
        );
        assert!(!json_obj.get("sync").unwrap().as_bool().unwrap());
        assert_eq!(
            json_obj.get("account_id").unwrap().as_u64().unwrap() as usize,
            account_id
        );

        // 反序列化
        let deserialized = ExecutionCommand::from_flatten(obj).unwrap();

        // 验证反序列化结果
        assert_eq!(deserialized.account_id, account_id);

        match &deserialized.cmd {
            Command::Async(AsyncCommand::CancelOrderExt(args)) => {
                assert_eq!(args.symbol.to_string(), symbol.to_string());
                if let OrderId::Id(id_str) = &args.order_id {
                    assert_eq!(id_str, "12345");
                } else {
                    panic!("order_id类型不匹配");
                }
            }
            _ => panic!("反序列化后命令类型不匹配"),
        }
    }

    #[test]
    fn test_ext_commands_flatten() {
        // 测试一些新增的ext命令的扁平化序列化和反序列化

        // 1. 测试MaxPositionExt
        let account_id: AccountId = 100;
        let symbol = Symbol::new("BTC");
        let extra = HashMap::new();

        let args = GetMaxPositionParams {
            symbol: symbol.clone(),
            extra: extra.clone(),
            leverage: 10,
        };

        let cmd = ExecutionCommand {
            account_id,
            context: Context::default(),
            cmd: Command::Sync(SyncCommand::MaxPositionExt(args)),
        };

        // 序列化为扁平化格式
        let flattened = cmd.to_flatten().unwrap();
        println!(
            "MaxPosition扁平化: {}",
            serde_json::to_string_pretty(&flattened).unwrap()
        );

        // 验证序列化结果包含正确的字段
        let obj = flattened.as_object().unwrap();
        assert_eq!(obj.get("method").unwrap().as_str().unwrap(), "MaxPosition");
        assert_eq!(obj.get("account_id").unwrap().as_u64().unwrap(), 100);

        // 检查symbol，不使用unwrap避免故障
        let symbol_json = obj.get("symbol").expect("应该有symbol字段");
        let symbol_str = symbol_json.to_string();
        assert!(
            symbol_str.contains("BTC") && symbol_str.contains("USDT"),
            "Symbol应该包含BTC和USDT，实际值: {symbol_str}"
        );

        // 反序列化测试
        let deserialized = ExecutionCommand::from_flatten(flattened).unwrap();

        // 验证反序列化结果
        assert_eq!(deserialized.account_id, account_id);
        match &deserialized.cmd {
            Command::Sync(SyncCommand::MaxPositionExt(args)) => {
                let symbol_str = args.symbol.to_string();
                assert!(
                    symbol_str.contains("BTC") && symbol_str.contains("USDT"),
                    "Symbol应该包含BTC和USDT，实际值: {symbol_str}"
                );
            }
            _ => panic!("反序列化后命令类型不匹配"),
        }

        // 2. 测试SetLeverageExt
        let args = SetLeverageParams {
            symbol: symbol.clone(),
            leverage: 20,
            extra: extra.clone(),
        };

        let cmd = ExecutionCommand {
            account_id,
            context: Context::default(),
            cmd: Command::Sync(SyncCommand::SetLeverageExt(args)),
        };

        // 序列化为扁平化格式
        let flattened = cmd.to_flatten().unwrap();
        println!(
            "SetLeverage扁平化: {}",
            serde_json::to_string_pretty(&flattened).unwrap()
        );

        // 验证序列化结果
        let obj = flattened.as_object().unwrap();
        assert_eq!(obj.get("method").unwrap().as_str().unwrap(), "SetLeverage");
        assert_eq!(obj.get("leverage").unwrap().as_u64().unwrap(), 20);

        // 反序列化测试
        let deserialized = ExecutionCommand::from_flatten(flattened).unwrap();

        // 验证反序列化结果
        match &deserialized.cmd {
            Command::Sync(SyncCommand::SetLeverageExt(args)) => {
                assert_eq!(args.leverage, 20);
            }
            _ => panic!("反序列化后命令类型不匹配"),
        }

        // 3. 测试SetDualSidePositionExt
        let args = SetDualSideParams {
            is_dual_side: true,
            extra: extra.clone(),
        };

        let cmd = ExecutionCommand {
            account_id,
            context: Context::default(),
            cmd: Command::Sync(SyncCommand::SetDualSidePositionExt(args)),
        };

        // 序列化为扁平化格式
        let flattened = cmd.to_flatten().unwrap();
        println!(
            "SetDualSidePosition扁平化: {}",
            serde_json::to_string_pretty(&flattened).unwrap()
        );

        // 验证序列化结果
        let obj = flattened.as_object().unwrap();
        assert_eq!(
            obj.get("method").unwrap().as_str().unwrap(),
            "SetDualSidePosition"
        );
        assert!(obj.get("is_dual_side").unwrap().as_bool().unwrap());

        // 反序列化测试
        let deserialized = ExecutionCommand::from_flatten(flattened).unwrap();

        // 验证反序列化结果
        match &deserialized.cmd {
            Command::Sync(SyncCommand::SetDualSidePositionExt(args)) => {
                assert!(args.is_dual_side);
            }
            _ => panic!("反序列化后命令类型不匹配"),
        }

        // 4. 测试各种只有extra参数的命令
        let args = GetBalancesParams {
            extra: extra.clone(),
        };

        let cmd = ExecutionCommand {
            account_id,
            context: Context::default(),
            cmd: Command::Sync(SyncCommand::BalanceExt(args)),
        };

        // 序列化为扁平化格式
        let flattened = cmd.to_flatten().unwrap();
        println!(
            "Balance扁平化: {}",
            serde_json::to_string_pretty(&flattened).unwrap()
        );

        // 验证序列化结果
        let obj = flattened.as_object().unwrap();
        assert_eq!(obj.get("method").unwrap().as_str().unwrap(), "Balance");

        // 反序列化测试
        let deserialized = ExecutionCommand::from_flatten(flattened).unwrap();

        // 验证反序列化结果
        match &deserialized.cmd {
            Command::Sync(SyncCommand::BalanceExt(_)) => {
                // 成功解析
            }
            _ => panic!("反序列化后命令类型不匹配: {:?}", deserialized.cmd),
        }

        // 5. 测试传入完整JSON字符串解析
        let json_str = r#"{
            "method": "MarkPrice",
            "sync": true,
            "account_id": 100,
            "symbol": "BTC_USDT",
            "extra": {}
        }"#;

        // 从JSON字符串解析
        let cmd = ExecutionCommand::from_flatten_string(json_str).unwrap();

        // 验证解析结果
        assert_eq!(cmd.account_id, 100);
        match &cmd.cmd {
            Command::Sync(SyncCommand::MarkPriceExt(args)) => {
                assert_eq!(args.symbol.as_ref().unwrap().to_string(), "BTC_USDT");
            }
            _ => panic!("解析JSON字符串后命令类型不匹配: {:?}", cmd.cmd),
        }
    }

    #[test]
    fn test_empty_extra_serialize() {
        // 测试空extra字段在序列化时会被忽略
        let account_id: AccountId = 1;
        let _symbol = Symbol::new("BTC_USDT");
        let empty_extra = HashMap::new();

        // 使用 GetOrdersExt 命令测试
        let args = GetOrdersParams {
            symbol: Symbol::new("BTC_USDT"),
            start_time: 0,
            end_time: 100,
            extra: empty_extra,
        };

        let cmd = ExecutionCommand {
            account_id,
            context: Context::default(),
            cmd: Command::Sync(SyncCommand::GetOrdersExt(args)),
        };

        // 序列化为JSON
        let json = cmd.to_flatten().unwrap();
        let obj = json.as_object().unwrap();

        println!(
            "序列化JSON: {}",
            serde_json::to_string_pretty(&json).unwrap()
        );

        // 验证extra字段不存在
        assert!(!obj.contains_key("extra"), "extra字段应被忽略");
    }

    #[test]
    fn test_missing_extra_deserialize() {
        // 测试缺少extra字段时能正确设置为空HashMap
        let json_str = r#"{
            "method": "Balance",
            "sync": true,
            "account_id": 1
        }"#;

        let cmd = ExecutionCommand::from_flatten_string(json_str).unwrap();

        match &cmd.cmd {
            Command::Sync(SyncCommand::BalanceExt(args)) => {
                assert!(args.extra.is_empty(), "extra字段应为空HashMap");
            }
            _ => panic!("命令类型不匹配"),
        }
    }

    #[test]
    fn test_missing_sync_deserialize() {
        // 测试缺少sync字段时默认为false
        let json_str = r#"{
            "method": "PlaceOrder",
            "account_id": 1,
            "order": {
                "cid": "test1234567",
                "symbol": "BTC_USDT",
                "order_type": "Limit",
                "side": "Buy",
                "pos_side": "Long",
                "price": 50000.0,
                "amount": 0.1,
                "time_in_force": "GTC"
            },
            "params": {
                "is_dual_side": false,
                "leverage": 10,
                "margin_mode": "Cross",
                "market_order_mode": "Normal"
            }
        }"#;

        let cmd = ExecutionCommand::from_flatten_string(json_str).unwrap();

        // 验证命令被解析为异步命令（因为没有sync字段，默认为false）
        match &cmd.cmd {
            Command::Async(_) => {}
            _ => panic!("默认应该是异步命令"),
        }
    }

    #[test]
    fn test_kline_flatten() {
        use quant_common::base::{KlineInterval, traits::GetKlineParams};

        // 测试K线命令的扁平化
        let account_id: AccountId = 123;
        let symbol = Symbol::new("BTC_USDT");
        let extra = HashMap::new();

        // 创建GetKlineParams
        let args = GetKlineParams {
            symbol: symbol.clone(),
            interval: KlineInterval::Min1,
            start_time: Some(*************), // 2023-01-01
            end_time: Some(*************),   // 2023-01-02
            limit: Some(100),
            extra: extra.clone(),
        };

        let cmd = ExecutionCommand {
            account_id,
            context: Context::default(),
            cmd: Command::Sync(SyncCommand::KlineExt(args)),
        };

        // 序列化为扁平化格式
        let obj = cmd.to_flatten().unwrap();
        let json_str = serde_json::to_string_pretty(&obj).unwrap();
        println!("K线扁平化: {json_str}");

        // 验证序列化结果
        let json_obj = obj.as_object().unwrap();
        assert_eq!(json_obj.get("method").unwrap().as_str().unwrap(), "Kline");
        assert!(json_obj.get("sync").unwrap().as_bool().unwrap());
        assert_eq!(
            json_obj.get("account_id").unwrap().as_u64().unwrap() as usize,
            account_id
        );

        // 验证K线特有字段
        assert!(json_obj.contains_key("symbol"));
        assert!(json_obj.contains_key("interval"));
        assert!(json_obj.contains_key("start_time"));
        assert!(json_obj.contains_key("end_time"));
        assert!(json_obj.contains_key("limit"));

        // 反序列化
        let deserialized = ExecutionCommand::from_flatten(obj).unwrap();

        // 验证反序列化结果
        assert_eq!(deserialized.account_id, account_id);

        match &deserialized.cmd {
            Command::Sync(SyncCommand::KlineExt(args)) => {
                let symbol_str = args.symbol.to_string();
                assert!(symbol_str.contains("BTC") && symbol_str.contains("USDT"));
                assert_eq!(args.interval, KlineInterval::Min1);
                assert_eq!(args.start_time, Some(*************));
                assert_eq!(args.end_time, Some(*************));
                assert_eq!(args.limit, Some(100));
            }
            _ => panic!("反序列化后命令类型不匹配"),
        }
    }

    #[test]
    fn test_kline_from_json_string() {
        // 测试从JSON字符串反序列化K线命令
        let json_str = r#"{
            "method": "Kline",
            "sync": true,
            "account_id": 1,
            "symbol": "BTC_USDT",
            "interval": "1m",
            "start_time": *************,
            "end_time": *************,
            "limit": 100
        }"#;

        // 从JSON字符串反序列化
        let cmd = ExecutionCommand::from_flatten_string(json_str).unwrap();

        // 验证反序列化结果
        assert_eq!(cmd.account_id, 1);
        match &cmd.cmd {
            Command::Sync(SyncCommand::KlineExt(args)) => {
                let symbol_str = args.symbol.to_string();
                assert!(symbol_str.contains("BTC") && symbol_str.contains("USDT"));
                assert_eq!(args.interval, KlineInterval::Min1);
                assert_eq!(args.start_time, Some(*************));
                assert_eq!(args.end_time, Some(*************));
                assert_eq!(args.limit, Some(100));
            }
            _ => panic!("反序列化后命令类型不匹配"),
        }
    }

    #[test]
    fn test_kline_minimal_params() {
        // 测试只有必需参数的K线命令
        let json_str = r#"{
            "method": "Kline",
            "sync": true,
            "account_id": 1,
            "symbol": "ETH_USDT",
            "interval": "5m"
        }"#;

        // 从JSON字符串反序列化
        let cmd = ExecutionCommand::from_flatten_string(json_str).unwrap();

        // 验证反序列化结果
        match &cmd.cmd {
            Command::Sync(SyncCommand::KlineExt(args)) => {
                let symbol_str = args.symbol.to_string();
                assert!(symbol_str.contains("ETH") && symbol_str.contains("USDT"));
                assert_eq!(args.interval, KlineInterval::Min5);
                assert_eq!(args.start_time, None);
                assert_eq!(args.end_time, None);
                assert_eq!(args.limit, None);
                assert!(args.extra.is_empty());
            }
            _ => panic!("反序列化后命令类型不匹配"),
        }
    }
}
