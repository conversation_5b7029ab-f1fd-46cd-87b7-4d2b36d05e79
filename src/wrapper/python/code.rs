//! Python策略代码加载和处理模块
//!
//! 该模块负责:
//! - 加载Python策略文件
//! - 处理Python模块的依赖关系
//! - 维护Python模块的命名空间

// use algo_common::msg::StrategyFile;
use quant_common::{Result, qerror};
use sonic_rs::Value;
use std::{ffi::CString, fs::read_to_string, path::PathBuf};
use trader::launcher::Launcher;

/// Python策略信息
#[derive(Debug)]
pub struct PyStrategyInfo {
    /// 主策略代码
    pub strategy: CString,
    /// 依赖的Python模块列表
    pub modules: Vec<PyModuleInfo>,
    /// 策略配置
    pub config: Value,
}

/// Python模块信息
#[derive(Debug)]
pub struct PyModuleInfo {
    /// Python模块名 (如 "utils.foo")
    pub name: CString,
    /// 文件名 (如 "utils/foo.py")
    pub file_name: CString,
    /// 模块代码
    pub code: CString,
}

/// 获取策略相关信息
///
/// # Arguments
///
/// * `launcher` - 策略启动器
///
/// # Returns
///
/// 返回包含策略代码、依赖模块和配置的信息结构
pub fn get_strategy_info(launcher: &mut Launcher, strategy_path: &str) -> Result<PyStrategyInfo> {
    match launcher.config().launcher_mode {
        trader::config::LauncherMode::Bin => load_from_files(launcher, strategy_path),
        trader::config::LauncherMode::AlgoRunner => unimplemented!(),
    }
}

/// 从本地文件加载策略
fn load_from_files(launcher: &Launcher, strategy_path: &str) -> Result<PyStrategyInfo> {
    let config = launcher.config();
    let path = PathBuf::from(strategy_path);

    // 加载主策略代码
    let strategy_code = read_to_string(&path)
        .map_err(|e| qerror!("读取python策略文件{}失败: {}", path.display(), e))?;
    let strategy_code = CString::new(strategy_code)?;

    // 加载策略配置
    let config: Value = match config.strategy_config_path.as_ref() {
        Some(path) => {
            if path.is_empty() {
                Value::new_object()
            } else {
                let config_str = read_to_string(path)
                    .map_err(|e| qerror!("读取python策略配置文件{}失败: {}", path, e))?;
                let toml_value: toml::Value = toml::from_str(&config_str)?;
                sonic_rs::to_value(&toml_value)?
            }
        }
        None => Value::new_object(),
    };

    Ok(PyStrategyInfo {
        strategy: strategy_code,
        modules: vec![],
        config,
    })
}

// 从AlgoRunner加载策略
// fn load_from_algo_runner(launcher: &mut Launcher) -> Result<PyStrategyInfo> {
//     let mut strategy = launcher
//         .strategy
//         .take()
//         .ok_or_else(|| qerror!("strategy is not initialized"))?;

//     let config = strategy.config.strategy;
//     let codes = strategy
//         .code
//         .take()
//         .ok_or_else(|| qerror!("code is not initialized"))?;

//     let mut modules = Vec::new();
//     let mut strategy_code = None;

//     // 处理所有Python文件
//     for file in codes {
//         if let Some(content) = process_file(&file, &mut modules)? {
//             strategy_code = Some(content);
//         }
//     }

//     let strategy_code = strategy_code.ok_or_else(|| qerror!("strategy.py not found"))?;

//     Ok(PyStrategyInfo {
//         strategy: CString::new(strategy_code)?,
//         modules,
//         config,
//     })
// }

// /// 处理单个Python文件或目录
// ///
// /// # Arguments
// ///
// /// * `file` - 策略文件信息
// /// * `parent_path` - 父模块路径
// /// * `modules` - 模块列表
// ///
// /// # Returns
// ///
// /// 如果是strategy.py则返回其内容，否则返回None
// fn process_file(file: &StrategyFile, modules: &mut Vec<PyModuleInfo>) -> Result<Option<String>> {
//     match file.is_file {
//         true => process_python_file(file, modules),
//         false => process_directory(file, modules),
//     }
// }

// /// 处理Python源文件
// fn process_python_file(
//     file: &StrategyFile,
//     _modules: &mut [PyModuleInfo],
// ) -> Result<Option<String>> {
//     if file.name == "strategy.py" {
//         Ok(Some(file.content.clone()))
//     } else if file.name.ends_with(".py") {
//         // 根据文件路径及名称写入文件
//         let path = &file.path;
//         let file_path = PathBuf::from(path);
//         if let Some(parent) = file_path.parent() {
//             std::fs::create_dir_all(parent)?;
//         }
//         std::fs::write(file_path, &file.content)?;

//         // 将文件路径转换为模块路径 删除第一级
//         // let path = &file.path;
//         // let path = path.split_once('/').map(|(_, rest)| rest).unwrap_or(path);
//         // let path = path.trim_end_matches(".py");
//         // let module_path = path.replace('/', ".");
//         // modules.push(PyModuleInfo {
//         //     name: CString::new(module_path)?,
//         //     file_name: CString::new(file.name.clone())?,
//         //     code: CString::new(file.content.clone())?,
//         // });
//         Ok(None)
//     } else {
//         Ok(None)
//     }
// }

// 处理目录
// fn process_directory(
//     dir: &StrategyFile,
//     modules: &mut Vec<PyModuleInfo>,
// ) -> Result<Option<String>> {
//     for file in &dir.sub_files {
//         if let Some(content) = process_file(file, modules)? {
//             return Ok(Some(content));
//         }
//     }
//     Ok(None)
// }
