use crate::wrapper::python::trader::utils::create_cid;
use ::trader::cache::Cache;
use ::trader::model::context::Context;
use ::trader::model::event::ex_command::Command;
use ::trader::model::event::ex_command::ExecutionCommand;
use ::trader::model::event::ex_command::SyncCommand;
use pyo3::Bound;
use pyo3::Py;
use pyo3::PyAny;
use pyo3::PyErr;
use pyo3::PyResult;
use pyo3::Python;
use pyo3::exceptions::PyTypeError;
use pyo3::pyclass;
use pyo3::pymethods;
use pyo3::pymodule;
use pyo3::types::PyAnyMethods;
use pyo3::types::PyDict;
use pyo3::types::PyModule;
use pyo3::types::PyModuleMethods;
use pyo3::wrap_pyfunction;
use pythonize::{depythonize, pythonize};
use quant_common::Result;
use quant_common::base::AccountMode;
use quant_common::base::Exchange;
use quant_common::base::MarginMode;
use quant_common::base::Order;
use quant_common::base::OrderId;
use quant_common::base::SubTransfer;
use quant_common::base::Transfer;
use quant_common::base::UserRequest;
use quant_common::base::WithDrawlParams;
use quant_common::base::traits::*;
use serde_json::Value;
use std::collections::HashMap;
use std::str::FromStr;
use trader::model::event::ex_command::AsyncCommand;
// 导入WebClient
use super::Trader;
use super::nb8::WebClientWrapper;

/// Trader结构体
///
/// 核心交易者结构体，作为Python与Rust交互的主要接口
/// 负责管理交易执行引擎、DEX同步、缓存等核心组件
#[pyclass]
pub struct TraderV2(pub Trader);

/// 为Trader结构体实现Python导出方法
/// 这些方法将被直接暴露给Python环境使用
#[pymethods]
impl TraderV2 {
    /// 执行交易指令
    ///
    /// 接收Python传入的交易指令，反序列化后通过Rust引擎执行
    ///
    /// # 参数
    /// * `py` - Python解释器引用
    /// * `cmd` - Python传入的命令对象
    ///
    /// # 返回
    /// * 指令执行结果，序列化为Python对象
    fn publish(&self, py: Python, cmd: Bound<'_, PyAny>) -> PyResult<Py<PyAny>> {
        let value_array: Value = depythonize(&cmd)?;
        let cmd = ExecutionCommand::from_flatten(value_array)
            .map_err(|e| PyTypeError::new_err(format!("无法反序列化Python指令: {e}")))?;

        // 使用允许线程执行，防止阻塞Python GIL
        let v = py.allow_threads(move || {
            // 在Tokio运行时中执行指令
            self.0
                .handle
                .block_on(async move { self.0.handle_cmd(cmd).await })
                .map_err(|e| PyErr::new::<PyAny, _>(e.to_string()))
        })?;

        // 将Rust结果序列化为Python对象
        pythonize(py, &v)
            .map_err(|e| PyTypeError::new_err(e.to_string()))
            .map(Bound::unbind)
    }

    /// 优雅退出 执行kill -2 信号
    fn graceful_shutdown(&self) -> PyResult<()> {
        // 查询当前进程id
        let pid = std::process::id();
        // 发送kill -2 信号
        std::process::Command::new("kill")
            .arg("-2")
            .arg(pid.to_string())
            .spawn()
            .map_err(|e| PyErr::new::<PyAny, _>(e.to_string()))?;
        Ok(())
    }

    /// 批量执行交易指令
    ///
    /// # 参数
    /// * `py` - Python解释器引用
    /// * `cmds` - 多个Python命令对象列表
    ///
    /// # 返回
    /// * 多个指令执行结果的列表
    fn batch_publish(&self, py: Python, cmds: Vec<Bound<'_, PyAny>>) -> PyResult<Vec<Py<PyAny>>> {
        let mut results = Vec::with_capacity(cmds.len());
        for cmd in cmds {
            results.push(self.publish(py, cmd)?);
        }
        Ok(results)
    }

    /// 记录日志到控制台和Python代码
    /// # 参数
    /// * `py` - Python解释器引用
    /// * `msg` - 日志消息内容
    /// * `level` - 日志级别，可选，默认为INFO
    /// * `color` - 颜色，可选，默认为None
    /// * `web` - 是否发送到Web平台，可选，默认为true
    #[pyo3(signature = (msg, level=None, color=None, web=Some(true)))]
    pub fn log(
        &self,
        msg: &str,
        level: Option<&str>,
        color: Option<&str>,
        web: Option<bool>,
    ) -> PyResult<()> {
        // 设置默认值
        let level = level.unwrap_or("INFO");
        let web = web.unwrap_or(false);
        let msg = if let Some(color) = color {
            if web {
                WebClientWrapper::color_msg(msg, color)?
            } else {
                msg.to_string()
            }
        } else {
            msg.to_string()
        };

        // 定义日志记录宏，根据参数生成日志调用
        macro_rules! log_with_level {
            ($level:ident) => {
                if let Some(event) = None::<String> {
                    $level!(event = event, web = web, "{}", msg);
                } else {
                    $level!(web = web, "{}", msg);
                }
            };
        }

        // 根据日志级别调用对应的日志宏
        match level {
            "TRACE" | "trace" => log_with_level!(trace),
            "DEBUG" | "debug" => log_with_level!(debug),
            "INFO" | "info" => log_with_level!(info),
            "WARN" | "warn" => log_with_level!(warn),
            "ERROR" | "error" => log_with_level!(error),
            unknown => warn!("Unknown log level: {}", unknown),
        }
        Ok(())
    }

    /// 限频日志记录工具
    ///
    /// 每N秒最多打印一次指定标签的日志
    ///
    /// # 参数
    /// * `tag` - 日志标识
    /// * `msg` - 日志内容
    /// * `color` - 颜色，可选，默认为None
    /// * `interval` - 日志最小间隔（秒），默认为0
    /// * `level` - 日志级别，可选，默认为INFO
    /// * `query` - 是否只查询不更新时间，默认为false
    ///
    /// # 返回
    /// * `bool` - true表示已记录日志，false表示未记录日志
    #[allow(clippy::too_many_arguments)]
    #[pyo3(signature = (tag, msg, color=None, interval=0, level=None, query=false))]
    pub fn tlog(
        &self,
        tag: &str,
        msg: &str,
        color: Option<&str>,
        interval: i64,
        level: Option<&str>,
        query: bool,
    ) -> PyResult<bool> {
        // 检查是否应该记录日志
        let should_proceed = self.0.throttled_log(tag, interval, query);

        let web = Some(should_proceed);

        // 使用log方法记录日志
        self.log(msg, level, color, web)?;

        // 返回是否记录了日志
        Ok(should_proceed)
    }

    /// 指定时间记录日志
    /// # 参数
    /// * `py` - Python解释器引用
    /// * `message` - 日志内容
    /// * `color` - 颜色，可选，默认为None
    /// * `time` - 日志时间
    /// * `level` - 日志级别
    /// * `web` - 是否发送到Web平台
    ///
    #[pyo3(signature = (message, time, color=None, level=None))]
    pub fn logt(
        &self,
        py: Python,
        message: &str,
        time: i64,
        color: Option<&str>,
        level: Option<&str>,
    ) -> PyResult<()> {
        let level = level.unwrap_or("INFO");
        let web_client = self.0.get_web_client()?;
        let handle = self.0.handle.clone();
        let msg = if let Some(color) = color {
            WebClientWrapper::color_msg(message, color)?
        } else {
            message.to_string()
        };
        py.allow_threads(move || {
            handle.block_on(async move { web_client.log_internal(&msg, level, Some(time)).await })
        })
    }

    /// 保存缓存数据
    ///
    /// # 参数
    /// * `py` - Python解释器引用
    /// * `data` - 要保存的数据dict
    fn cache_save(&self, data: Bound<'_, PyAny>) -> PyResult<()> {
        // 反序列化为Value
        let data: Value = depythonize(&data)?;
        // 序列化为JSON字符串
        let data = serde_json::to_string_pretty(&data).unwrap();
        // 保存到缓存
        self.0
            .cache
            .save(data)
            .map_err(|e| PyErr::new::<PyAny, _>(e.to_string()))
    }

    /// 加载缓存数据
    ///
    /// # 参数
    /// * `py` - Python解释器引用
    ///
    /// # 返回
    /// * 缓存数据dict，如果没有则返回None
    fn cache_load(&self, py: Python) -> PyResult<Option<Py<PyAny>>> {
        let cache = self
            .0
            .cache
            .load()
            .map_err(|e| PyErr::new::<PyAny, _>(e.to_string()))?;
        let cache = cache.map(|s| serde_json::from_str::<Value>(&s).unwrap());
        match cache {
            Some(cache) => {
                let cache =
                    pythonize(py, &cache).map_err(|e| PyErr::new::<PyAny, _>(e.to_string()))?;
                let cache = Bound::unbind(cache);
                Ok(Some(cache))
            }
            None => Ok(None),
        }
    }

    /// 创建cid
    /// # 参数
    /// * `exchange` - 交易所名称
    /// # 返回
    /// * 创建的cid
    fn create_cid(&self, exchange: Bound<'_, PyAny>) -> PyResult<String> {
        let exchange: Exchange = depythonize(&exchange)
        .map_err(|e| {
            format!(
                "Failed to convert Python exchange to Rust exchange: {e:?}, py exchange: {exchange:?}"
            )
        })
        .map_err(PyErr::new::<PyAny, _>)?;
        Ok(exchange.create_cid(None))
    }

    /// 发送HTTP请求
    ///
    /// # 参数
    /// * `py` - Python解释器引用
    /// * `url` - 请求URL
    /// * `method` - 请求方法（GET, POST等）
    /// * `body` - 请求体，可选，接受Python字典
    /// * `headers` - 请求头，可选
    ///
    /// # 返回
    /// * 响应数据，转换为Python对象
    #[pyo3(signature = (url, method, body, headers=None))]
    fn http_request(
        &self,
        py: Python,
        url: &str,
        method: &str,
        body: Option<&Bound<'_, PyAny>>,
        headers: Option<HashMap<String, String>>,
    ) -> PyResult<Py<PyAny>> {
        // 在进入allow_threads之前处理Python对象
        let body_string = if let Some(body_obj) = body {
            // 将Python对象反序列化为Value然后序列化为JSON字符串
            let value: Value = depythonize(body_obj)
                .map_err(|e| PyTypeError::new_err(format!("无法反序列化Python对象: {e}")))?;
            serde_json::to_string(&value)
                .map_err(|e| PyTypeError::new_err(format!("无法序列化为JSON: {e}")))?
        } else {
            String::new()
        };

        let res = py.allow_threads(move || {
            // 准备请求参数
            let headers = headers.unwrap_or_default();
            let method = method.to_string();
            let url = url.to_string();

            // 解析HTTP方法
            let method = reqwest::Method::from_str(&method)
                .map_err(|e| PyErr::new::<PyAny, _>(e.to_string()))?;

            // 转换头信息为reqwest格式
            let headers =
                reqwest::header::HeaderMap::from_iter(headers.into_iter().map(|(k, v)| {
                    (
                        reqwest::header::HeaderName::from_str(&k).unwrap(),
                        reqwest::header::HeaderValue::from_str(&v).unwrap(),
                    )
                }));

            // 创建HTTP客户端并发送请求
            let client = reqwest::Client::new();

            let res: Result<Value> = self.0.handle.block_on(async move {
                let res = client
                    .request(method, url)
                    .headers(headers)
                    .body(body_string)
                    .send()
                    .await?;
                let body = res.json::<Value>().await?;
                Ok(body)
            });
            res
        });
        pythonize(py, &res)
            .map_err(|e| PyTypeError::new_err(e.to_string()))
            .map(Bound::unbind)
    }

    /// 创建并初始化WebClient
    ///
    /// # 参数
    /// * `py` - Python解释器引用
    /// * `config` - WebClient配置对象
    fn init_web_client(&mut self, _py: Python, config: Bound<'_, PyAny>) -> PyResult<()> {
        let web_client = WebClientWrapper::new(&config)?;
        self.0.web_client = Some(web_client);
        Ok(())
    }

    /// 启动WebClient
    ///
    /// 激活自动上传功能
    ///
    /// # 参数
    /// * `py` - Python解释器引用
    /// * `upload_interval` - 上传间隔（秒），可选，默认为5
    #[pyo3(signature = (upload_interval=None))]
    fn start_web_client(&mut self, py: Python, upload_interval: Option<i32>) -> PyResult<()> {
        let receiver = self.0.log_receiver.take();
        let mut web_client = self.0.get_web_client_clone()?;
        web_client.start(py, upload_interval, receiver)
    }

    /// 停止WebClient
    ///
    /// 停止自动上传功能
    ///
    /// # 参数
    /// * `py` - Python解释器引用
    fn stop_web_client(&self) -> PyResult<()> {
        let web_client = self.0.get_web_client()?;
        web_client.stop_py()
    }

    /// 设置实盘强停状态
    /// # 参数
    /// * `force_stop` - 是否强停
    fn set_force_stop(&self, force_stop: bool) -> PyResult<()> {
        let web_client = self.0.get_web_client()?;
        web_client.set_force_stop(force_stop);
        Ok(())
    }

    /// 检查策略是否被标记为强停/暂停交易
    ///
    /// # 参数
    /// * `py` - Python解释器引用
    ///
    /// # 返回
    /// * 布尔值，表示是否强停/暂停交易
    fn is_web_force_stopped(&self, py: Python) -> PyResult<bool> {
        let web_client = self.0.get_web_client()?;
        web_client.is_stopped(py)
    }

    /// 检查交易是否被缓停
    ///
    /// # 参数
    /// * `py` - Python解释器引用
    ///
    /// # 返回
    /// * 布尔值，表示交易是否被缓停
    fn is_web_soft_stopped(&self, py: Python) -> PyResult<bool> {
        let web_client = self.0.get_web_client()?;
        web_client.is_trading_disabled(py)
    }

    /// 检查是否停止开仓
    ///
    /// # 参数
    /// * `py` - Python解释器引用
    ///
    /// # 返回
    /// * 布尔值，表示是否停止开仓
    fn is_web_opening_stopped(&self, py: Python) -> PyResult<bool> {
        let web_client = self.0.get_web_client()?;
        web_client.is_opening_disabled(py)
    }

    /// 检查是否强平
    ///
    /// # 参数
    /// * `py` - Python解释器引用
    ///
    /// # 返回
    /// * 布尔值，表示是否强平
    fn is_web_force_closing(&self, py: Python) -> PyResult<bool> {
        let web_client = self.0.get_web_client()?;
        web_client.is_closing_enabled(py)
    }

    /// 更新余额信息
    /// # 参数
    /// * `py` - Python解释器引用
    /// * `primary_balance` - 主所余额
    /// * `secondary_balance` - 次所余额（可选）
    /// * `available_primary` - 主所可用余额（可选）
    /// * `available_secondary` - 次所可用余额（可选）
    #[pyo3(signature = (primary_balance, secondary_balance=None, available_primary=None, available_secondary=None))]
    fn update_total_balance(
        &self,
        py: Python,
        primary_balance: f64,
        secondary_balance: Option<f64>,
        available_primary: Option<f64>,
        available_secondary: Option<f64>,
    ) -> PyResult<()> {
        let web_client = self.0.get_web_client()?;
        web_client.update_total_balance(
            py,
            primary_balance,
            secondary_balance,
            available_primary,
            available_secondary,
        )
    }

    /// 更新所有节点持仓价值
    /// # 参数
    /// * `py` - Python解释器引用
    /// * `total_value` - 总价值
    /// * `long_position_value` - 多头持仓价值
    /// * `short_position_value` - 空头持仓价值
    fn update_total_position_value(
        &self,
        py: Python,
        total_value: f64,
        long_position_value: f64,
        short_position_value: f64,
    ) -> PyResult<()> {
        let web_client = self.0.get_web_client()?;
        web_client.update_total_position_value(
            py,
            total_value,
            long_position_value,
            short_position_value,
        )
    }

    /// 更新当前节点持仓价值
    /// # 参数
    /// * `py` - Python解释器引用
    /// * `total_value` - 总价值
    fn update_current_position_value(
        &self,
        py: Python,
        total_value: f64,
        long_position_value: f64,
        short_position_value: f64,
    ) -> PyResult<()> {
        let web_client = self.0.get_web_client()?;
        web_client.update_current_position_value(
            py,
            total_value,
            long_position_value,
            short_position_value,
        )
    }

    /// 添加资金费
    /// # 参数
    /// * `py` - Python解释器引用
    /// * `primary_fee` - 主所资金费（可选）
    /// * `secondary_fee` - 次所资金费（可选）
    #[pyo3(signature = (primary_fee=None, secondary_fee=None))]
    fn add_funding_fee(
        &self,
        py: Python,
        primary_fee: Option<f64>,
        secondary_fee: Option<f64>,
    ) -> PyResult<()> {
        let web_client = self.0.get_web_client()?;
        web_client.add_funding_fee(py, primary_fee, secondary_fee)
    }

    /// 更新预测资金费
    /// # 参数
    /// * `py` - Python解释器引用
    /// * `primary_fee` - 主所预测资金费（可选）
    /// * `secondary_fee` - 次所预测资金费（可选）
    #[pyo3(signature = (primary_fee=None, secondary_fee=None))]
    fn update_pred_funding(
        &self,
        py: Python,
        primary_fee: Option<f64>,
        secondary_fee: Option<f64>,
    ) -> PyResult<()> {
        let web_client = self.0.get_web_client()?;
        web_client.update_predicted_funding_fee(py, primary_fee, secondary_fee)
    }

    /// 更新浮动盈亏
    /// # 参数
    /// * `py` - Python解释器引用
    /// * `floating_profit` - 浮动盈亏
    fn update_floating_profit(&self, py: Python, floating_profit: f64) -> PyResult<()> {
        let web_client = self.0.get_web_client()?;
        web_client.update_floating_profit(py, floating_profit)
    }

    /// 更新交易统计
    /// # 参数
    /// * `py` - Python解释器引用
    /// * `maker_volume` - 挂单量
    /// * `taker_volume` - 吃单量
    /// * `profit` - 利润
    /// * `is_single_close` - 是否单腿平仓
    #[pyo3(signature = (maker_volume = 0., taker_volume = 0., profit = 0., is_single_close=false))]
    fn update_trade_stats(
        &self,
        py: Python,
        maker_volume: f64,
        taker_volume: f64,
        profit: f64,
        is_single_close: bool,
    ) -> PyResult<f64> {
        let web_client = self.0.get_web_client()?;
        let trade_volume = maker_volume + taker_volume;
        web_client.update_trade_stats(
            py,
            trade_volume,
            Some(maker_volume),
            Some(taker_volume),
            profit,
            is_single_close,
        )
    }

    /// 上传多个表格数据
    /// # 参数
    /// * `py` - Python解释器引用
    /// * `tables` - 表格数据列表
    fn upload_tables(&self, py: Python, tables: &Bound<'_, PyAny>) -> PyResult<()> {
        let web_client = self.0.get_web_client()?;
        web_client.upload_tables(py, tables)
    }

    /// 记录利润
    /// # 参数
    /// * `py` - Python解释器引用
    /// * `profit` - 利润值
    fn log_profit(&self, py: Python, profit: f64) -> PyResult<()> {
        let web_client = self.0.get_web_client()?;
        web_client.log_profit(py, profit)
    }

    /// 获取统计数据
    ///
    /// 返回当前统计数据，使用原始字段名（非序列化别名）
    ///
    /// # 参数
    /// * `py` - Python解释器引用
    ///
    /// # 返回
    /// * 包含统计数据的Python字典对象
    fn get_stats(&self, py: Python) -> PyResult<Py<PyAny>> {
        // 检查WebClient是否已初始化
        let web_client = self.0.get_web_client()?;

        // 获取StatsManager并生成JSON
        let stats_json = py
            .allow_threads(move || {
                let fut = async {
                    let stats_manager = web_client.stats_manager.lock().await;
                    serde_json::to_value(&*stats_manager)
                };
                self.0.handle.block_on(fut)
            })
            .map_err(|e| PyErr::new::<PyAny, _>(e.to_string()))?;

        // 将JSON转换为Python对象
        pythonize(py, &stats_json)
            .map_err(|e| {
                PyErr::new::<pyo3::exceptions::PyValueError, _>(format!(
                    "Failed to pythonize stats: {e}"
                ))
            })
            .map(Bound::unbind)
    }

    // -----------------------------------------------------------------------------------V2接口-----------------------------------------------------------------------------------

    /// 发送自定义请求
    ///
    /// 参数:
    ///     account_id: 账户ID
    ///     method: HTTP方法
    ///     path: 请求路径
    ///     auth: 是否需要认证
    ///     query: 请求参数
    ///     body: 请求数据体
    ///     url: 请求URL
    ///     headers: 请求头
    ///     generate: 是否只生成命令而不执行
    #[allow(clippy::too_many_arguments)]
    #[pyo3(signature = (account_id, method, path, auth, query=None, body=None, url=None, headers=None, generate=false))]
    fn request(
        &self,
        py: Python,
        account_id: usize,
        method: &str,
        path: &str,
        auth: bool,
        query: Option<&Bound<'_, PyDict>>,
        body: Option<&Bound<'_, PyAny>>,
        url: Option<&str>,
        headers: Option<&Bound<'_, PyDict>>,
        generate: bool,
    ) -> PyResult<Py<PyAny>> {
        let query: Option<HashMap<String, String>> = match query
            .map(|d| d.extract::<HashMap<String, String>>())
            .transpose()
        {
            Ok(q) => q,
            Err(e) => {
                error!("extract query error: {:?}", e);
                return Err(e);
            }
        };
        let query = match sonic_rs::to_value(&query) {
            Ok(q) => Some(q),
            Err(e) => {
                error!("to_value query error: {:?}", e);
                return Err(PyErr::new::<PyAny, _>(e.to_string()));
            }
        };

        let body: Option<HashMap<String, String>> = match body
            .map(|d| d.extract::<HashMap<String, String>>())
            .transpose()
        {
            Ok(q) => q,
            Err(e) => {
                error!("extract body error: {:?}", e);
                return Err(e);
            }
        };
        let body = match sonic_rs::to_value(&body) {
            Ok(q) => Some(q),
            Err(e) => {
                error!("to_value body error: {:?}", e);
                return Err(PyErr::new::<PyAny, _>(e.to_string()));
            }
        };

        let user_request = UserRequest {
            method: method.to_string(),
            path: path.to_string(),
            auth,
            query,
            body,
            url: url.map(|u| u.to_string()),
            headers: headers.map(|h| depythonize(h).unwrap()),
        };

        let cmd = Command::Sync(SyncCommand::Request(user_request));

        let cmd = ExecutionCommand {
            account_id,
            context: Context::default(),
            cmd,
        };

        if generate {
            return pythonize(py, &cmd.to_flatten())
                .map(Bound::unbind)
                .map_err(|e| PyTypeError::new_err(format!("无法序列化命令: {e}")));
        }

        let v = py.allow_threads(move || {
            self.0
                .handle
                .block_on(async move { self.0.handle_cmd(cmd).await })
        });
        v.map(|v| pythonize(py, &v))
            .map_err(|e| PyErr::new::<PyAny, _>(e.to_string()))?
            .map_err(|e| PyTypeError::new_err(format!("无法序列化命令: {e}")))
            .map(Bound::unbind)
    }

    /// 获取订单列表
    #[allow(clippy::too_many_arguments)]
    #[pyo3(signature = (account_id, symbol, start, end, extra=None, generate=false))]
    fn get_orders(
        &self,
        py: Python,
        account_id: usize,
        symbol: &str,
        start: i64,
        end: i64,
        extra: Option<&Bound<'_, PyDict>>,
        generate: bool,
    ) -> PyResult<Py<PyAny>> {
        let extra: HashMap<String, Value> =
            extra.map(|e| depythonize(e).unwrap()).unwrap_or_default();
        let symbol = serde_plain::from_str(symbol)
            .map_err(|e| PyTypeError::new_err(format!("无法解析symbol: {e}")))?;

        let cmd = SyncCommand::GetOrdersExt(GetOrdersParams {
            symbol,
            start_time: start,
            end_time: end,
            extra,
        });

        let cmd = ExecutionCommand {
            account_id,
            cmd: Command::Sync(cmd),
            context: Context::default(),
        };

        if generate {
            return pythonize(py, &cmd.to_flatten())
                .map(Bound::unbind)
                .map_err(|e| PyTypeError::new_err(format!("无法序列化命令: {e}")));
        }

        let v = py.allow_threads(move || {
            self.0
                .handle
                .block_on(async move { self.0.handle_cmd(cmd).await })
        });
        v.map(|v| pythonize(py, &v))
            .map_err(|e| PyErr::new::<PyAny, _>(e.to_string()))?
            .map_err(|e| PyTypeError::new_err(format!("无法序列化命令: {e}")))
            .map(Bound::unbind)
    }

    /// 获取未成交订单列表
    #[pyo3(signature = (account_id, symbol, extra=None, generate=false))]
    fn get_open_orders(
        &self,
        py: Python,
        account_id: usize,
        symbol: &str,
        extra: Option<&Bound<'_, PyDict>>,
        generate: bool,
    ) -> PyResult<Py<PyAny>> {
        let extra: HashMap<String, Value> =
            extra.map(|e| depythonize(e).unwrap()).unwrap_or_default();
        let symbol = serde_plain::from_str(symbol)
            .map_err(|e| PyTypeError::new_err(format!("无法解析symbol: {e}")))?;

        let params = GetOpenOrdersParams { symbol, extra };

        let cmd = Command::Sync(SyncCommand::GetOpenOrdersExt(params));

        let cmd = ExecutionCommand {
            account_id,
            context: Context::default(),
            cmd,
        };

        if generate {
            return pythonize(py, &cmd.to_flatten())
                .map(Bound::unbind)
                .map_err(|e| PyTypeError::new_err(format!("无法序列化命令: {e}")));
        }

        let v = py.allow_threads(move || {
            self.0
                .handle
                .block_on(async move { self.0.handle_cmd(cmd).await })
        });
        v.map(|v| pythonize(py, &v))
            .map_err(|e| PyErr::new::<PyAny, _>(e.to_string()))?
            .map_err(|e| PyTypeError::new_err(format!("无法序列化命令: {e}")))
            .map(Bound::unbind)
    }

    /// 获取所有未成交订单
    #[pyo3(signature = (account_id, extra=None, generate=false))]
    fn get_all_open_orders(
        &self,
        py: Python,
        account_id: usize,
        extra: Option<&Bound<'_, PyDict>>,
        generate: bool,
    ) -> PyResult<Py<PyAny>> {
        let extra: HashMap<String, Value> =
            extra.map(|e| depythonize(e).unwrap()).unwrap_or_default();

        let params = GetAllOpenOrdersParams { extra };

        let cmd = Command::Sync(SyncCommand::GetAllOpenOrdersExt(params));

        let cmd = ExecutionCommand {
            account_id,
            context: Context::default(),
            cmd,
        };

        if generate {
            return pythonize(py, &cmd.to_flatten())
                .map(Bound::unbind)
                .map_err(|e| PyTypeError::new_err(format!("无法序列化命令: {e}")));
        }

        let v = py.allow_threads(move || {
            self.0
                .handle
                .block_on(async move { self.0.handle_cmd(cmd).await })
        });
        v.map(|v| pythonize(py, &v))
            .map_err(|e| PyErr::new::<PyAny, _>(e.to_string()))?
            .map_err(|e| PyTypeError::new_err(format!("无法序列化命令: {e}")))
            .map(Bound::unbind)
    }

    /// 根据ID查询订单
    #[allow(clippy::too_many_arguments)]
    #[pyo3(signature = (account_id, symbol, order_id=None, cid=None, extra=None, generate=false))]
    fn get_order_by_id(
        &self,
        py: Python,
        account_id: usize,
        symbol: &str,
        order_id: Option<String>,
        cid: Option<String>,
        extra: Option<&Bound<'_, PyDict>>,
        generate: bool,
    ) -> PyResult<Py<PyAny>> {
        let extra: HashMap<String, Value> =
            extra.map(|e| depythonize(e).unwrap()).unwrap_or_default();
        let symbol = serde_plain::from_str(symbol)
            .map_err(|e| PyTypeError::new_err(format!("无法解析symbol: {e}")))?;
        let order_id = if let Some(id) = order_id {
            OrderId::Id(id)
        } else if let Some(cid) = cid {
            OrderId::ClientOrderId(cid)
        } else {
            return Err(PyTypeError::new_err("order_id 和 cid 不能同时为空"));
        };

        let params = GetOrderByIdParams {
            symbol,
            order_id,
            extra,
        };

        let cmd = Command::Sync(SyncCommand::GetOrderByIdExt(params));

        let cmd = ExecutionCommand {
            account_id,
            context: Context::default(),
            cmd,
        };

        if generate {
            return pythonize(py, &cmd.to_flatten())
                .map(Bound::unbind)
                .map_err(|e| PyTypeError::new_err(format!("无法序列化命令: {e}")));
        }

        let v = py.allow_threads(move || {
            self.0
                .handle
                .block_on(async move { self.0.handle_cmd(cmd).await })
        });
        v.map(|v| pythonize(py, &v))
            .map_err(|e| PyErr::new::<PyAny, _>(e.to_string()))?
            .map_err(|e| PyTypeError::new_err(format!("无法序列化命令: {e}")))
            .map(Bound::unbind)
    }

    /// 下单
    #[allow(clippy::too_many_arguments)]
    #[pyo3(signature = (account_id, order, params=None, extra=None, sync=true, generate=false))]
    fn place_order(
        &self,
        py: Python,
        account_id: usize,
        order: Bound<'_, PyAny>,
        params: Option<Bound<'_, PyAny>>,
        extra: Option<&Bound<'_, PyDict>>,
        sync: bool,
        generate: bool,
    ) -> PyResult<Py<PyAny>> {
        let order: Order = depythonize(&order)
            .map_err(|e| PyTypeError::new_err(format!("无法解析订单对象: {e}")))?;

        let params = if let Some(p) = params {
            Some(
                depythonize(&p)
                    .map_err(|e| PyTypeError::new_err(format!("无法解析参数对象: {e}")))?,
            )
        } else {
            None
        };

        let extra: HashMap<String, Value> =
            extra.map(|e| depythonize(e).unwrap()).unwrap_or_default();

        let cmd = PostOrderParams {
            order,
            params: params.unwrap_or_default(),
            extra,
        };

        let cmd = if sync {
            Command::Sync(SyncCommand::PlaceOrderExt(cmd))
        } else {
            Command::Async(AsyncCommand::PlaceOrderExt(cmd))
        };

        let cmd = ExecutionCommand {
            account_id,
            context: Context::default(),
            cmd,
        };

        if generate {
            return pythonize(py, &cmd.to_flatten())
                .map(Bound::unbind)
                .map_err(|e| PyTypeError::new_err(format!("无法序列化命令: {e}")));
        }

        let v = py.allow_threads(move || {
            self.0
                .handle
                .block_on(async move { self.0.handle_cmd(cmd).await })
        });
        v.map(|v| pythonize(py, &v))
            .map_err(|e| PyErr::new::<PyAny, _>(e.to_string()))?
            .map_err(|e| PyTypeError::new_err(format!("无法序列化命令: {e}")))
            .map(Bound::unbind)
    }

    /// 批量下单
    #[allow(clippy::too_many_arguments)]
    #[pyo3(signature = (account_id, orders, params=None, extra=None, sync=true, generate=false))]
    fn batch_place_order(
        &self,
        py: Python,
        account_id: usize,
        orders: Bound<'_, PyAny>,
        params: Option<Bound<'_, PyAny>>,
        extra: Option<&Bound<'_, PyDict>>,
        sync: bool,
        generate: bool,
    ) -> PyResult<Py<PyAny>> {
        let orders: Vec<Order> = depythonize(&orders)
            .map_err(|e| PyTypeError::new_err(format!("无法解析订单列表: {e}")))?;

        let params = if let Some(p) = params {
            Some(
                depythonize(&p)
                    .map_err(|e| PyTypeError::new_err(format!("无法解析参数对象: {e}")))?,
            )
        } else {
            None
        };

        let extra: HashMap<String, Value> =
            extra.map(|e| depythonize(e).unwrap()).unwrap_or_default();

        let cmd = if sync {
            Command::Sync(SyncCommand::BatchPlaceOrderExt(PostBatchOrderParams {
                orders: orders.clone(),
                params: params.clone().unwrap_or_default(),
                extra,
            }))
        } else {
            Command::Async(AsyncCommand::BatchPlaceOrderExt(PostBatchOrderParams {
                orders: orders.clone(),
                params: params.clone().unwrap_or_default(),
                extra,
            }))
        };

        let cmd = ExecutionCommand {
            account_id,
            context: Context::default(),
            cmd,
        };

        if generate {
            return pythonize(py, &cmd.to_flatten())
                .map(Bound::unbind)
                .map_err(|e| PyTypeError::new_err(format!("无法序列化命令: {e}")));
        }

        let v = py.allow_threads(move || {
            self.0
                .handle
                .block_on(async move { self.0.handle_cmd(cmd).await })
        });
        v.map(|v| pythonize(py, &v))
            .map_err(|e| PyErr::new::<PyAny, _>(e.to_string()))?
            .map_err(|e| PyTypeError::new_err(format!("无法序列化命令: {e}")))
            .map(Bound::unbind)
    }

    /// 修改订单
    #[pyo3(signature = (account_id, order, extra=None, sync=true, generate=false))]
    fn amend_order(
        &self,
        py: Python,
        account_id: usize,
        order: Bound<'_, PyAny>,
        extra: Option<&Bound<'_, PyDict>>,
        sync: bool,
        generate: bool,
    ) -> PyResult<Py<PyAny>> {
        let order: Order = depythonize(&order)
            .map_err(|e| PyTypeError::new_err(format!("无法解析订单对象: {e}")))?;

        let extra: HashMap<String, Value> =
            extra.map(|e| depythonize(e).unwrap()).unwrap_or_default();

        let cmd = if sync {
            Command::Sync(SyncCommand::AmendOrderExt(AmendOrderParams {
                order: order.clone(),
                extra,
            }))
        } else {
            Command::Async(AsyncCommand::AmendOrderExt(AmendOrderParams {
                order: order.clone(),
                extra,
            }))
        };

        let cmd = ExecutionCommand {
            account_id,
            context: Context::default(),
            cmd,
        };

        if generate {
            return pythonize(py, &cmd.to_flatten())
                .map(Bound::unbind)
                .map_err(|e| PyTypeError::new_err(format!("无法序列化命令: {e}")));
        }

        let v = py.allow_threads(move || {
            self.0
                .handle
                .block_on(async move { self.0.handle_cmd(cmd).await })
        });
        v.map(|v| pythonize(py, &v))
            .map_err(|e| PyErr::new::<PyAny, _>(e.to_string()))?
            .map_err(|e| PyTypeError::new_err(format!("无法序列化命令: {e}")))
            .map(Bound::unbind)
    }

    /// 取消订单
    #[allow(clippy::too_many_arguments)]
    #[pyo3(signature = (account_id, symbol, order_id=None, cid=None, extra=None, sync=true, generate=false))]
    fn cancel_order(
        &self,
        py: Python,
        account_id: usize,
        symbol: &str,
        order_id: Option<String>,
        cid: Option<String>,
        extra: Option<&Bound<'_, PyDict>>,
        sync: bool,
        generate: bool,
    ) -> PyResult<Py<PyAny>> {
        let extra: HashMap<String, Value> =
            extra.map(|e| depythonize(e).unwrap()).unwrap_or_default();
        let symbol = serde_plain::from_str(symbol)
            .map_err(|e| PyTypeError::new_err(format!("无法解析symbol: {e}")))?;
        let order_id = if let Some(id) = order_id {
            OrderId::Id(id)
        } else if let Some(cid) = cid {
            OrderId::ClientOrderId(cid)
        } else {
            return Err(PyTypeError::new_err("order_id 和 cid 不能同时为空"));
        };

        let cmd = if sync {
            Command::Sync(SyncCommand::CancelOrderExt(CancelOrderParams {
                symbol,
                order_id,
                extra,
            }))
        } else {
            Command::Async(AsyncCommand::CancelOrderExt(CancelOrderParams {
                symbol,
                order_id,
                extra,
            }))
        };

        let cmd = ExecutionCommand {
            account_id,
            context: Context::default(),
            cmd,
        };

        if generate {
            return pythonize(py, &cmd.to_flatten())
                .map(Bound::unbind)
                .map_err(|e| PyTypeError::new_err(format!("无法序列化命令: {e}")));
        }

        let v = py.allow_threads(move || {
            self.0
                .handle
                .block_on(async move { self.0.handle_cmd(cmd).await })
        });
        v.map(|v| pythonize(py, &v))
            .map_err(|e| PyErr::new::<PyAny, _>(e.to_string()))?
            .map_err(|e| PyTypeError::new_err(format!("无法序列化命令: {e}")))
            .map(Bound::unbind)
    }

    /// 批量取消订单
    #[pyo3(signature = (account_id, symbol, extra=None, sync=true, generate=false))]
    fn batch_cancel_order(
        &self,
        py: Python,
        account_id: usize,
        symbol: &str,
        extra: Option<&Bound<'_, PyDict>>,
        sync: bool,
        generate: bool,
    ) -> PyResult<Py<PyAny>> {
        let extra: HashMap<String, Value> =
            extra.map(|e| depythonize(e).unwrap()).unwrap_or_default();
        let symbol = serde_plain::from_str(symbol)
            .map_err(|e| PyTypeError::new_err(format!("无法解析symbol: {e}")))?;

        let cmd = if sync {
            Command::Sync(SyncCommand::BatchCancelOrderExt(BatchCancelOrderParams {
                symbol,
                extra,
            }))
        } else {
            Command::Async(AsyncCommand::BatchCancelOrderExt(BatchCancelOrderParams {
                symbol,
                extra,
            }))
        };

        let cmd = ExecutionCommand {
            account_id,
            context: Context::default(),
            cmd,
        };

        if generate {
            return pythonize(py, &cmd.to_flatten())
                .map(Bound::unbind)
                .map_err(|e| PyTypeError::new_err(format!("无法序列化命令: {e}")));
        }

        let v = py.allow_threads(move || {
            self.0
                .handle
                .block_on(async move { self.0.handle_cmd(cmd).await })
        });
        v.map(|v| pythonize(py, &v))
            .map_err(|e| PyErr::new::<PyAny, _>(e.to_string()))?
            .map_err(|e| PyTypeError::new_err(format!("无法序列化命令: {e}")))
            .map(Bound::unbind)
    }

    /// 获取持仓信息
    #[pyo3(signature = (account_id, symbol, extra=None, generate=false))]
    fn get_position(
        &self,
        py: Python,
        account_id: usize,
        symbol: &str,
        extra: Option<&Bound<'_, PyDict>>,
        generate: bool,
    ) -> PyResult<Py<PyAny>> {
        let extra: HashMap<String, Value> =
            extra.map(|e| depythonize(e).unwrap()).unwrap_or_default();
        let symbol = serde_plain::from_str(symbol)
            .map_err(|e| PyTypeError::new_err(format!("无法解析symbol: {e}")))?;

        let params = GetPositionParams { symbol, extra };

        let cmd = Command::Sync(SyncCommand::PositionExt(params));

        let cmd = ExecutionCommand {
            account_id,
            context: Context::default(),
            cmd,
        };

        if generate {
            return pythonize(py, &cmd.to_flatten())
                .map(Bound::unbind)
                .map_err(|e| PyTypeError::new_err(format!("无法序列化命令: {e}")));
        }

        let v = py.allow_threads(move || {
            self.0
                .handle
                .block_on(async move { self.0.handle_cmd(cmd).await })
        });
        v.map(|v| pythonize(py, &v))
            .map_err(|e| PyErr::new::<PyAny, _>(e.to_string()))?
            .map_err(|e| PyTypeError::new_err(format!("无法序列化命令: {e}")))
            .map(Bound::unbind)
    }

    /// 获取所有持仓
    #[pyo3(signature = (account_id, extra=None, generate=false))]
    fn get_positions(
        &self,
        py: Python,
        account_id: usize,
        extra: Option<&Bound<'_, PyDict>>,
        generate: bool,
    ) -> PyResult<Py<PyAny>> {
        let extra: HashMap<String, Value> =
            extra.map(|e| depythonize(e).unwrap()).unwrap_or_default();

        let cmd = Command::Sync(SyncCommand::PositionsExt(GetPositionsParams { extra }));

        let cmd = ExecutionCommand {
            account_id,
            context: Context::default(),
            cmd,
        };

        if generate {
            return pythonize(py, &cmd.to_flatten())
                .map(Bound::unbind)
                .map_err(|e| PyTypeError::new_err(format!("无法序列化命令: {e}")));
        }

        let v = py.allow_threads(move || {
            self.0
                .handle
                .block_on(async move { self.0.handle_cmd(cmd).await })
        });
        v.map(|v| pythonize(py, &v))
            .map_err(|e| PyErr::new::<PyAny, _>(e.to_string()))?
            .map_err(|e| PyTypeError::new_err(format!("无法序列化命令: {e}")))
            .map(Bound::unbind)
    }

    /// 获取最大持仓
    #[pyo3(signature = (account_id, symbol, level=None, extra=None, generate=false))]
    fn get_max_position(
        &self,
        py: Python,
        account_id: usize,
        symbol: &str,
        level: Option<f64>,
        extra: Option<&Bound<'_, PyDict>>,
        generate: bool,
    ) -> PyResult<Py<PyAny>> {
        let extra: HashMap<String, Value> =
            extra.map(|e| depythonize(e).unwrap()).unwrap_or_default();
        let symbol = serde_plain::from_str(symbol)
            .map_err(|e| PyTypeError::new_err(format!("无法解析symbol: {e}")))?;
        let leverage = level.unwrap_or(0.0) as u8;

        let params = GetMaxPositionParams {
            symbol,
            leverage,
            extra,
        };

        let cmd = Command::Sync(SyncCommand::MaxPositionExt(params));

        let cmd = ExecutionCommand {
            account_id,
            context: Context::default(),
            cmd,
        };

        if generate {
            return pythonize(py, &cmd.to_flatten())
                .map(Bound::unbind)
                .map_err(|e| PyTypeError::new_err(format!("无法序列化命令: {e}")));
        }

        let v = py.allow_threads(move || {
            self.0
                .handle
                .block_on(async move { self.0.handle_cmd(cmd).await })
        });
        v.map(|v| pythonize(py, &v))
            .map_err(|e| PyErr::new::<PyAny, _>(e.to_string()))?
            .map_err(|e| PyTypeError::new_err(format!("无法序列化命令: {e}")))
            .map(Bound::unbind)
    }

    /// 获取USDT余额
    #[pyo3(signature = (account_id, extra=None, generate=false))]
    fn get_usdt_balance(
        &self,
        py: Python,
        account_id: usize,
        extra: Option<&Bound<'_, PyDict>>,
        generate: bool,
    ) -> PyResult<Py<PyAny>> {
        let extra: HashMap<String, Value> =
            extra.map(|e| depythonize(e).unwrap()).unwrap_or_default();

        let cmd = Command::Sync(SyncCommand::UsdtBalanceExt(GetUsdtBalanceParams { extra }));

        let cmd = ExecutionCommand {
            account_id,
            context: Context::default(),
            cmd,
        };

        if generate {
            return pythonize(py, &cmd.to_flatten())
                .map(Bound::unbind)
                .map_err(|e| PyTypeError::new_err(format!("无法序列化命令: {e}")));
        }

        let v = py.allow_threads(move || {
            self.0
                .handle
                .block_on(async move { self.0.handle_cmd(cmd).await })
        });
        v.map(|v| pythonize(py, &v))
            .map_err(|e| PyErr::new::<PyAny, _>(e.to_string()))?
            .map_err(|e| PyTypeError::new_err(format!("无法序列化命令: {e}")))
            .map(Bound::unbind)
    }

    /// 获取所有币种余额
    #[pyo3(signature = (account_id, extra=None, generate=false))]
    fn get_balances(
        &self,
        py: Python,
        account_id: usize,
        extra: Option<&Bound<'_, PyDict>>,
        generate: bool,
    ) -> PyResult<Py<PyAny>> {
        let extra: HashMap<String, Value> =
            extra.map(|e| depythonize(e).unwrap()).unwrap_or_default();

        let cmd = Command::Sync(SyncCommand::BalanceExt(GetBalancesParams { extra }));

        let cmd = ExecutionCommand {
            account_id,
            context: Context::default(),
            cmd,
        };

        if generate {
            return pythonize(py, &cmd.to_flatten())
                .map(Bound::unbind)
                .map_err(|e| PyTypeError::new_err(format!("无法序列化命令: {e}")));
        }

        let v = py.allow_threads(move || {
            self.0
                .handle
                .block_on(async move { self.0.handle_cmd(cmd).await })
        });
        v.map(|v| pythonize(py, &v))
            .map_err(|e| PyErr::new::<PyAny, _>(e.to_string()))?
            .map_err(|e| PyTypeError::new_err(format!("无法序列化命令: {e}")))
            .map(Bound::unbind)
    }

    /// 获取指定币种余额
    #[pyo3(signature = (account_id, asset, extra=None, generate=false))]
    fn get_balance_by_coin(
        &self,
        py: Python,
        account_id: usize,
        asset: &str,
        extra: Option<&Bound<'_, PyDict>>,
        generate: bool,
    ) -> PyResult<Py<PyAny>> {
        let extra: HashMap<String, Value> =
            extra.map(|e| depythonize(e).unwrap()).unwrap_or_default();

        let cmd = Command::Sync(SyncCommand::BalanceByCoinExt(GetBalanceParams {
            asset: asset.to_string(),
            extra,
        }));

        let cmd = ExecutionCommand {
            account_id,
            context: Context::default(),
            cmd,
        };

        if generate {
            return pythonize(py, &cmd.to_flatten())
                .map(Bound::unbind)
                .map_err(|e| PyTypeError::new_err(format!("无法序列化命令: {e}")));
        }

        let v = py.allow_threads(move || {
            self.0
                .handle
                .block_on(async move { self.0.handle_cmd(cmd).await })
        });
        v.map(|v| pythonize(py, &v))
            .map_err(|e| PyErr::new::<PyAny, _>(e.to_string()))?
            .map_err(|e| PyTypeError::new_err(format!("无法序列化命令: {e}")))
            .map(Bound::unbind)
    }

    /// 获取费率信息
    #[pyo3(signature = (account_id, symbol, extra=None, generate=false))]
    fn get_fee_rate(
        &self,
        py: Python,
        account_id: usize,
        symbol: &str,
        extra: Option<&Bound<'_, PyDict>>,
        generate: bool,
    ) -> PyResult<Py<PyAny>> {
        let extra: HashMap<String, Value> =
            extra.map(|e| depythonize(e).unwrap()).unwrap_or_default();
        let symbol = serde_plain::from_str(symbol)
            .map_err(|e| PyTypeError::new_err(format!("无法解析symbol: {e}")))?;

        let cmd = Command::Sync(SyncCommand::FeeRateExt(GetFeeRateParams { symbol, extra }));

        let cmd = ExecutionCommand {
            account_id,
            context: Context::default(),
            cmd,
        };

        if generate {
            return pythonize(py, &cmd.to_flatten())
                .map(Bound::unbind)
                .map_err(|e| PyTypeError::new_err(format!("无法序列化命令: {e}")));
        }

        let v = py.allow_threads(move || {
            self.0
                .handle
                .block_on(async move { self.0.handle_cmd(cmd).await })
        });
        v.map(|v| pythonize(py, &v))
            .map_err(|e| PyErr::new::<PyAny, _>(e.to_string()))?
            .map_err(|e| PyTypeError::new_err(format!("无法序列化命令: {e}")))
            .map(Bound::unbind)
    }

    /// 获取费用折扣信息
    #[pyo3(signature = (account_id, extra=None, generate=false))]
    fn get_fee_discount_info(
        &self,
        py: Python,
        account_id: usize,
        extra: Option<&Bound<'_, PyDict>>,
        generate: bool,
    ) -> PyResult<Py<PyAny>> {
        let extra: HashMap<String, Value> =
            extra.map(|e| depythonize(e).unwrap()).unwrap_or_default();

        let cmd = Command::Sync(SyncCommand::GetFeeDiscountInfoExt(
            GetFeeDiscountInfoParams { extra },
        ));

        let cmd = ExecutionCommand {
            account_id,
            context: Context::default(),
            cmd,
        };

        if generate {
            return pythonize(py, &cmd.to_flatten())
                .map(Bound::unbind)
                .map_err(|e| PyTypeError::new_err(format!("无法序列化命令: {e}")));
        }

        let v = py.allow_threads(move || {
            self.0
                .handle
                .block_on(async move { self.0.handle_cmd(cmd).await })
        });
        v.map(|v| pythonize(py, &v))
            .map_err(|e| PyErr::new::<PyAny, _>(e.to_string()))?
            .map_err(|e| PyTypeError::new_err(format!("无法序列化命令: {e}")))
            .map(Bound::unbind)
    }

    /// 检查费用折扣是否已启用
    #[pyo3(signature = (account_id, extra=None, generate=false))]
    fn is_fee_discount_enabled(
        &self,
        py: Python,
        account_id: usize,
        extra: Option<&Bound<'_, PyDict>>,
        generate: bool,
    ) -> PyResult<Py<PyAny>> {
        let extra: HashMap<String, Value> =
            extra.map(|e| depythonize(e).unwrap()).unwrap_or_default();

        let params = IsFeeDiscountEnabledParams { extra };

        let cmd = Command::Sync(SyncCommand::IsFeeDiscountEnabledExt(params));

        let cmd = ExecutionCommand {
            account_id,
            context: Context::default(),
            cmd,
        };

        if generate {
            return pythonize(py, &cmd.to_flatten())
                .map(Bound::unbind)
                .map_err(|e| PyTypeError::new_err(format!("无法序列化命令: {e}")));
        }

        let v = py.allow_threads(move || {
            self.0
                .handle
                .block_on(async move { self.0.handle_cmd(cmd).await })
        });
        v.map(|v| pythonize(py, &v))
            .map_err(|e| PyErr::new::<PyAny, _>(e.to_string()))?
            .map_err(|e| PyTypeError::new_err(format!("无法序列化命令: {e}")))
            .map(Bound::unbind)
    }

    /// 设置费用折扣启用状态
    #[pyo3(signature = (account_id, enabled, extra=None, generate=false))]
    fn set_fee_discount_enabled(
        &self,
        py: Python,
        account_id: usize,
        enabled: bool,
        extra: Option<&Bound<'_, PyDict>>,
        generate: bool,
    ) -> PyResult<Py<PyAny>> {
        let extra: HashMap<String, Value> =
            extra.map(|e| depythonize(e).unwrap()).unwrap_or_default();

        let params = SetFeeDiscountEnabledParams {
            enable: enabled,
            extra,
        };

        let cmd = Command::Sync(SyncCommand::SetFeeDiscountEnabledExt(params));

        let cmd = ExecutionCommand {
            account_id,
            context: Context::default(),
            cmd,
        };

        if generate {
            return pythonize(py, &cmd.to_flatten())
                .map(Bound::unbind)
                .map_err(|e| PyTypeError::new_err(format!("无法序列化命令: {e}")));
        }

        let v = py.allow_threads(move || {
            self.0
                .handle
                .block_on(async move { self.0.handle_cmd(cmd).await })
        });
        v.map(|v| pythonize(py, &v))
            .map_err(|e| PyErr::new::<PyAny, _>(e.to_string()))?
            .map_err(|e| PyTypeError::new_err(format!("无法序列化命令: {e}")))
            .map(Bound::unbind)
    }

    /// 检查是否是双向持仓
    #[pyo3(signature = (account_id, extra=None, generate=false))]
    fn is_dual_side(
        &self,
        py: Python,
        account_id: usize,
        extra: Option<&Bound<'_, PyDict>>,
        generate: bool,
    ) -> PyResult<Py<PyAny>> {
        let extra: HashMap<String, Value> =
            extra.map(|e| depythonize(e).unwrap()).unwrap_or_default();

        let params = IsDualSideParams { extra };

        let cmd = Command::Sync(SyncCommand::IsDualSidePositionExt(params));

        let cmd = ExecutionCommand {
            account_id,
            context: Context::default(),
            cmd,
        };

        if generate {
            return pythonize(py, &cmd.to_flatten())
                .map(Bound::unbind)
                .map_err(|e| PyTypeError::new_err(format!("无法序列化命令: {e}")));
        }

        let v = py.allow_threads(move || {
            self.0
                .handle
                .block_on(async move { self.0.handle_cmd(cmd).await })
        });
        v.map(|v| pythonize(py, &v))
            .map_err(|e| PyErr::new::<PyAny, _>(e.to_string()))?
            .map_err(|e| PyTypeError::new_err(format!("无法序列化命令: {e}")))
            .map(Bound::unbind)
    }

    /// 设置双向持仓模式
    #[pyo3(signature = (account_id, dual_side, extra=None, generate=false))]
    fn set_dual_side(
        &self,
        py: Python,
        account_id: usize,
        dual_side: bool,
        extra: Option<&Bound<'_, PyDict>>,
        generate: bool,
    ) -> PyResult<Py<PyAny>> {
        let extra: HashMap<String, Value> =
            extra.map(|e| depythonize(e).unwrap()).unwrap_or_default();

        let params = SetDualSideParams {
            is_dual_side: dual_side,
            extra,
        };

        let cmd = Command::Sync(SyncCommand::SetDualSidePositionExt(params));

        let cmd = ExecutionCommand {
            account_id,
            context: Context::default(),
            cmd,
        };

        if generate {
            return pythonize(py, &cmd.to_flatten())
                .map(Bound::unbind)
                .map_err(|e| PyTypeError::new_err(format!("无法序列化命令: {e}")));
        }

        let v = py.allow_threads(move || {
            self.0
                .handle
                .block_on(async move { self.0.handle_cmd(cmd).await })
        });
        v.map(|v| pythonize(py, &v))
            .map_err(|e| PyErr::new::<PyAny, _>(e.to_string()))?
            .map_err(|e| PyTypeError::new_err(format!("无法序列化命令: {e}")))
            .map(Bound::unbind)
    }

    #[allow(clippy::too_many_arguments)]
    /// 获取K线数据
    #[pyo3(signature = (account_id, symbol, interval, start_time=None, end_time=None, limit=None, extra=None, generate=false))]
    fn get_kline(
        &self,
        py: Python,
        account_id: usize,
        symbol: &str,
        interval: &str,
        start_time: Option<i64>,
        end_time: Option<i64>,
        limit: Option<u32>,
        extra: Option<&Bound<'_, PyDict>>,
        generate: bool,
    ) -> PyResult<Py<PyAny>> {
        let extra: HashMap<String, Value> =
            extra.map(|e| depythonize(e).unwrap()).unwrap_or_default();

        let symbol = serde_plain::from_str(symbol)
            .map_err(|e| PyTypeError::new_err(format!("无法解析symbol: {e}")))?;
        let interval = serde_plain::from_str(interval)
            .map_err(|e| PyTypeError::new_err(format!("无法解析interval: {e}")))?;

        let params = GetKlineParams {
            symbol,
            interval,
            start_time,
            end_time,
            limit,
            extra,
        };

        let cmd = Command::Sync(SyncCommand::KlineExt(params));

        let cmd = ExecutionCommand {
            account_id,
            context: Context::default(),
            cmd,
        };

        if generate {
            return pythonize(py, &cmd.to_flatten())
                .map(Bound::unbind)
                .map_err(|e| PyTypeError::new_err(format!("无法序列化命令: {e}")));
        }

        let v = py.allow_threads(move || {
            self.0
                .handle
                .block_on(async move { self.0.handle_cmd(cmd).await })
        });
        v.map(|v| pythonize(py, &v))
            .map_err(|e| PyErr::new::<PyAny, _>(e.to_string()))?
            .map_err(|e| PyTypeError::new_err(format!("无法序列化命令: {e}")))
            .map(Bound::unbind)
    }
    /// 获取行情数据
    #[pyo3(signature = (account_id, symbol, extra=None, generate=false))]
    fn get_ticker(
        &self,
        py: Python,
        account_id: usize,
        symbol: &str,
        extra: Option<&Bound<'_, PyDict>>,
        generate: bool,
    ) -> PyResult<Py<PyAny>> {
        let extra: HashMap<String, Value> =
            extra.map(|e| depythonize(e).unwrap()).unwrap_or_default();
        let symbol = serde_plain::from_str(symbol)
            .map_err(|e| PyTypeError::new_err(format!("无法解析symbol: {e}")))?;

        let params = GetTickerParams { symbol, extra };

        let cmd = Command::Sync(SyncCommand::TickerExt(params));

        let cmd = ExecutionCommand {
            account_id,
            context: Context::default(),
            cmd,
        };

        if generate {
            return pythonize(py, &cmd.to_flatten())
                .map(Bound::unbind)
                .map_err(|e| PyTypeError::new_err(format!("无法序列化命令: {e}")));
        }

        let v = py.allow_threads(move || {
            self.0
                .handle
                .block_on(async move { self.0.handle_cmd(cmd).await })
        });
        v.map(|v| pythonize(py, &v))
            .map_err(|e| PyErr::new::<PyAny, _>(e.to_string()))?
            .map_err(|e| PyTypeError::new_err(format!("无法序列化命令: {e}")))
            .map(Bound::unbind)
    }

    /// 获取所有行情数据
    #[pyo3(signature = (account_id, extra=None, generate=false))]
    fn get_tickers(
        &self,
        py: Python,
        account_id: usize,
        extra: Option<&Bound<'_, PyDict>>,
        generate: bool,
    ) -> PyResult<Py<PyAny>> {
        let extra: HashMap<String, Value> =
            extra.map(|e| depythonize(e).unwrap()).unwrap_or_default();

        let params = GetTickersParams { extra };

        let cmd = Command::Sync(SyncCommand::TickersExt(params));

        let cmd = ExecutionCommand {
            account_id,
            context: Context::default(),
            cmd,
        };

        if generate {
            return pythonize(py, &cmd.to_flatten())
                .map(Bound::unbind)
                .map_err(|e| PyTypeError::new_err(format!("无法序列化命令: {e}")));
        }

        let v = py.allow_threads(move || {
            self.0
                .handle
                .block_on(async move { self.0.handle_cmd(cmd).await })
        });
        v.map(|v| pythonize(py, &v))
            .map_err(|e| PyErr::new::<PyAny, _>(e.to_string()))?
            .map_err(|e| PyTypeError::new_err(format!("无法序列化命令: {e}")))
            .map(Bound::unbind)
    }

    /// 查询结算资金费
    #[allow(clippy::too_many_arguments)]
    #[pyo3(signature = (account_id, symbol, start_time=None, end_time=None, extra=None, generate=false))]
    fn get_funding_fee(
        &self,
        py: Python,
        account_id: usize,
        symbol: &str,
        start_time: Option<i64>,
        end_time: Option<i64>,
        extra: Option<&Bound<'_, PyDict>>,
        generate: bool,
    ) -> PyResult<Py<PyAny>> {
        let extra: HashMap<String, Value> =
            extra.map(|e| depythonize(e).unwrap()).unwrap_or_default();
        let symbol = serde_plain::from_str(symbol)
            .map_err(|e| PyTypeError::new_err(format!("无法解析symbol: {e}")))?;

        let params = GetFundingFeeParams {
            symbol,
            start_time,
            end_time,
            extra,
        };

        let cmd = Command::Sync(SyncCommand::FundingFeeExt(params));

        let cmd = ExecutionCommand {
            account_id,
            context: Context::default(),
            cmd,
        };

        if generate {
            return pythonize(py, &cmd.to_flatten())
                .map(Bound::unbind)
                .map_err(|e| PyTypeError::new_err(format!("无法序列化命令: {e}")));
        }

        let v = py.allow_threads(move || {
            self.0
                .handle
                .block_on(async move { self.0.handle_cmd(cmd).await })
        });
        v.map(|v| pythonize(py, &v))
            .map_err(|e| PyErr::new::<PyAny, _>(e.to_string()))?
            .map_err(|e| PyTypeError::new_err(format!("无法序列化命令: {e}")))
            .map(Bound::unbind)
    }

    /// 获取最大杠杆
    #[pyo3(signature = (account_id, symbol, extra=None, generate=false))]
    fn get_max_leverage(
        &self,
        py: Python,
        account_id: usize,
        symbol: &str,
        extra: Option<&Bound<'_, PyDict>>,
        generate: bool,
    ) -> PyResult<Py<PyAny>> {
        let extra: HashMap<String, Value> =
            extra.map(|e| depythonize(e).unwrap()).unwrap_or_default();
        let symbol = serde_plain::from_str(symbol)
            .map_err(|e| PyTypeError::new_err(format!("无法解析symbol: {e}")))?;

        let params = GetMaxLeverageParams { symbol, extra };

        let cmd = Command::Sync(SyncCommand::MaxLeverageExt(params));

        let cmd = ExecutionCommand {
            account_id,
            context: Context::default(),
            cmd,
        };

        if generate {
            return pythonize(py, &cmd.to_flatten())
                .map(Bound::unbind)
                .map_err(|e| PyTypeError::new_err(format!("无法序列化命令: {e}")));
        }

        let v = py.allow_threads(move || {
            self.0
                .handle
                .block_on(async move { self.0.handle_cmd(cmd).await })
        });
        v.map(|v| pythonize(py, &v))
            .map_err(|e| PyErr::new::<PyAny, _>(e.to_string()))?
            .map_err(|e| PyTypeError::new_err(format!("无法序列化命令: {e}")))
            .map(Bound::unbind)
    }

    /// 查询保证金模式
    #[pyo3(signature = (account_id, symbol, margin_coin, extra=None, generate=false))]
    fn get_margin_mode(
        &self,
        py: Python,
        account_id: usize,
        symbol: &str,
        margin_coin: &str,
        extra: Option<&Bound<'_, PyDict>>,
        generate: bool,
    ) -> PyResult<Py<PyAny>> {
        let extra: HashMap<String, Value> =
            extra.map(|e| depythonize(e).unwrap()).unwrap_or_default();
        let symbol = serde_plain::from_str(symbol)
            .map_err(|e| PyTypeError::new_err(format!("无法解析symbol: {e}")))?;

        let params = GetMarginModeParams {
            symbol,
            margin_coin: margin_coin.to_string(),
            extra,
        };

        let cmd = Command::Sync(SyncCommand::MarginModeExt(params));

        let cmd = ExecutionCommand {
            account_id,
            context: Context::default(),
            cmd,
        };

        if generate {
            return pythonize(py, &cmd.to_flatten())
                .map(Bound::unbind)
                .map_err(|e| PyTypeError::new_err(format!("无法序列化命令: {e}")));
        }

        let v = py.allow_threads(move || {
            self.0
                .handle
                .block_on(async move { self.0.handle_cmd(cmd).await })
        });
        v.map(|v| pythonize(py, &v))
            .map_err(|e| PyErr::new::<PyAny, _>(e.to_string()))?
            .map_err(|e| PyTypeError::new_err(format!("无法序列化命令: {e}")))
            .map(Bound::unbind)
    }

    /// 设置保证金模式
    #[allow(clippy::too_many_arguments)]
    #[pyo3(signature = (account_id, symbol, margin_coin, margin_mode, extra=None, generate=false))]
    fn set_margin_mode(
        &self,
        py: Python,
        account_id: usize,
        symbol: &str,
        margin_coin: &str,
        margin_mode: Bound<'_, PyAny>,
        extra: Option<&Bound<'_, PyDict>>,
        generate: bool,
    ) -> PyResult<Py<PyAny>> {
        let extra: HashMap<String, Value> =
            extra.map(|e| depythonize(e).unwrap()).unwrap_or_default();
        let symbol = serde_plain::from_str(symbol)
            .map_err(|e| PyTypeError::new_err(format!("无法解析symbol: {e}")))?;
        let margin_mode: MarginMode = depythonize(&margin_mode)
            .map_err(|e| PyTypeError::new_err(format!("无法解析保证金模式: {e}")))?;

        let params = SetMarginModeParams {
            symbol,
            margin_coin: margin_coin.to_string(),
            margin_mode,
            extra,
        };

        let cmd = Command::Sync(SyncCommand::SetMarginModeExt(params));

        let cmd = ExecutionCommand {
            account_id,
            context: Context::default(),
            cmd,
        };

        if generate {
            return pythonize(py, &cmd.to_flatten())
                .map(Bound::unbind)
                .map_err(|e| PyTypeError::new_err(format!("无法序列化命令: {e}")));
        }

        let v = py.allow_threads(move || {
            self.0
                .handle
                .block_on(async move { self.0.handle_cmd(cmd).await })
        });
        v.map(|v| pythonize(py, &v))
            .map_err(|e| PyErr::new::<PyAny, _>(e.to_string()))?
            .map_err(|e| PyTypeError::new_err(format!("无法序列化命令: {e}")))
            .map(Bound::unbind)
    }

    /// 设置杠杆
    #[pyo3(signature = (account_id, symbol, leverage, extra=None, generate=false))]
    fn set_leverage(
        &self,
        py: Python,
        account_id: usize,
        symbol: &str,
        leverage: u8,
        extra: Option<&Bound<'_, PyDict>>,
        generate: bool,
    ) -> PyResult<Py<PyAny>> {
        let extra: HashMap<String, Value> =
            extra.map(|e| depythonize(e).unwrap()).unwrap_or_default();
        let symbol = serde_plain::from_str(symbol)
            .map_err(|e| PyTypeError::new_err(format!("无法解析symbol: {e}")))?;

        let params = SetLeverageParams {
            symbol,
            leverage,
            extra,
        };

        let cmd = Command::Sync(SyncCommand::SetLeverageExt(params));

        let cmd = ExecutionCommand {
            account_id,
            context: Context::default(),
            cmd,
        };

        if generate {
            return pythonize(py, &cmd.to_flatten())
                .map(Bound::unbind)
                .map_err(|e| PyTypeError::new_err(format!("无法序列化命令: {e}")));
        }

        let v = py.allow_threads(move || {
            self.0
                .handle
                .block_on(async move { self.0.handle_cmd(cmd).await })
        });
        v.map(|v| pythonize(py, &v))
            .map_err(|e| PyErr::new::<PyAny, _>(e.to_string()))?
            .map_err(|e| PyTypeError::new_err(format!("无法序列化命令: {e}")))
            .map(Bound::unbind)
    }

    /// 万向划转
    #[pyo3(signature = (account_id, transfer, extra=None, generate=false))]
    fn transfer(
        &self,
        py: Python,
        account_id: usize,
        transfer: Bound<'_, PyAny>,
        extra: Option<&Bound<'_, PyDict>>,
        generate: bool,
    ) -> PyResult<Py<PyAny>> {
        let extra: HashMap<String, Value> =
            extra.map(|e| depythonize(e).unwrap()).unwrap_or_default();
        let transfer: Transfer = depythonize(&transfer)
            .map_err(|e| PyTypeError::new_err(format!("无法解析划转对象: {e}")))?;

        let params = TransferParams { transfer, extra };

        let cmd = Command::Sync(SyncCommand::TransferExt(params));

        let cmd = ExecutionCommand {
            account_id,
            context: Context::default(),
            cmd,
        };

        if generate {
            return pythonize(py, &cmd.to_flatten())
                .map(Bound::unbind)
                .map_err(|e| PyTypeError::new_err(format!("无法序列化命令: {e}")));
        }

        let v = py.allow_threads(move || {
            self.0
                .handle
                .block_on(async move { self.0.handle_cmd(cmd).await })
        });
        v.map(|v| pythonize(py, &v))
            .map_err(|e| PyErr::new::<PyAny, _>(e.to_string()))?
            .map_err(|e| PyTypeError::new_err(format!("无法序列化命令: {e}")))
            .map(Bound::unbind)
    }

    /// 子账户划转
    #[pyo3(signature = (account_id, sub_transfer, extra=None, generate=false))]
    fn sub_transfer(
        &self,
        py: Python,
        account_id: usize,
        sub_transfer: Bound<'_, PyAny>,
        extra: Option<&Bound<'_, PyDict>>,
        generate: bool,
    ) -> PyResult<Py<PyAny>> {
        let extra: HashMap<String, Value> =
            extra.map(|e| depythonize(e).unwrap()).unwrap_or_default();
        let sub_transfer: SubTransfer = depythonize(&sub_transfer)
            .map_err(|e| PyTypeError::new_err(format!("无法解析子账户划转对象: {e}")))?;

        let params = SubTransferParams {
            transfer: sub_transfer,
            extra,
        };

        let cmd = Command::Sync(SyncCommand::SubTransferExt(params));

        let cmd = ExecutionCommand {
            account_id,
            context: Context::default(),
            cmd,
        };

        if generate {
            return pythonize(py, &cmd.to_flatten())
                .map(Bound::unbind)
                .map_err(|e| PyTypeError::new_err(format!("无法序列化命令: {e}")));
        }

        let v = py.allow_threads(move || {
            self.0
                .handle
                .block_on(async move { self.0.handle_cmd(cmd).await })
        });
        v.map(|v| pythonize(py, &v))
            .map_err(|e| PyErr::new::<PyAny, _>(e.to_string()))?
            .map_err(|e| PyTypeError::new_err(format!("无法序列化命令: {e}")))
            .map(Bound::unbind)
    }

    /// 获取充值地址
    #[allow(clippy::too_many_arguments)]
    #[pyo3(signature = (account_id, ccy, chain=None, amount=None, extra=None, generate=false))]
    fn get_deposit_address(
        &self,
        py: Python,
        account_id: usize,
        ccy: &str,
        chain: Option<Bound<'_, PyAny>>,
        amount: Option<f64>,
        extra: Option<&Bound<'_, PyDict>>,
        generate: bool,
    ) -> PyResult<Py<PyAny>> {
        let extra: HashMap<String, Value> =
            extra.map(|e| depythonize(e).unwrap()).unwrap_or_default();

        let chain = if let Some(c) = chain {
            Some(
                depythonize(&c)
                    .map_err(|e| PyTypeError::new_err(format!("无法解析链信息: {e}")))?,
            )
        } else {
            None
        };

        let params = GetDepositAddressParams {
            ccy: ccy.to_string(),
            chain,
            amount,
            extra,
        };

        let cmd = Command::Sync(SyncCommand::GetDepositAddressExt(params));

        let cmd = ExecutionCommand {
            account_id,
            context: Context::default(),
            cmd,
        };

        if generate {
            return pythonize(py, &cmd.to_flatten())
                .map(Bound::unbind)
                .map_err(|e| PyTypeError::new_err(format!("无法序列化命令: {e}")));
        }

        let v = py.allow_threads(move || {
            self.0
                .handle
                .block_on(async move { self.0.handle_cmd(cmd).await })
        });
        v.map(|v| pythonize(py, &v))
            .map_err(|e| PyErr::new::<PyAny, _>(e.to_string()))?
            .map_err(|e| PyTypeError::new_err(format!("无法序列化命令: {e}")))
            .map(Bound::unbind)
    }

    /// 提币
    #[pyo3(signature = (account_id, withdrawal, extra=None, generate=false))]
    fn withdrawal(
        &self,
        py: Python,
        account_id: usize,
        withdrawal: Bound<'_, PyAny>,
        extra: Option<&Bound<'_, PyDict>>,
        generate: bool,
    ) -> PyResult<Py<PyAny>> {
        let extra: HashMap<String, Value> =
            extra.map(|e| depythonize(e).unwrap()).unwrap_or_default();

        let withdrawal: WithDrawlParams = depythonize(&withdrawal)
            .map_err(|e| PyTypeError::new_err(format!("无法解析提币参数: {e}")))?;

        let params = WithdrawalParams {
            params: withdrawal,
            extra,
        };

        let cmd = Command::Sync(SyncCommand::WithdrawalExt(params));

        let cmd = ExecutionCommand {
            account_id,
            context: Context::default(),
            cmd,
        };

        if generate {
            return pythonize(py, &cmd.to_flatten())
                .map(Bound::unbind)
                .map_err(|e| PyTypeError::new_err(format!("无法序列化命令: {e}")));
        }

        let v = py.allow_threads(move || {
            self.0
                .handle
                .block_on(async move { self.0.handle_cmd(cmd).await })
        });
        v.map(|v| pythonize(py, &v))
            .map_err(|e| PyErr::new::<PyAny, _>(e.to_string()))?
            .map_err(|e| PyTypeError::new_err(format!("无法序列化命令: {e}")))
            .map(Bound::unbind)
    }

    /// 获取账户信息
    #[pyo3(signature = (account_id, extra=None, generate=false))]
    fn get_account_info(
        &self,
        py: Python,
        account_id: usize,
        extra: Option<&Bound<'_, PyDict>>,
        generate: bool,
    ) -> PyResult<Py<PyAny>> {
        let extra: HashMap<String, Value> =
            extra.map(|e| depythonize(e).unwrap()).unwrap_or_default();

        let params = GetAccountInfoParams { extra };

        let cmd = Command::Sync(SyncCommand::GetAccountInfoExt(params));

        let cmd = ExecutionCommand {
            account_id,
            context: Context::default(),
            cmd,
        };

        if generate {
            return pythonize(py, &cmd.to_flatten())
                .map(Bound::unbind)
                .map_err(|e| PyTypeError::new_err(format!("无法序列化命令: {e}")));
        }

        let v = py.allow_threads(move || {
            self.0
                .handle
                .block_on(async move { self.0.handle_cmd(cmd).await })
        });
        v.map(|v| pythonize(py, &v))
            .map_err(|e| PyErr::new::<PyAny, _>(e.to_string()))?
            .map_err(|e| PyTypeError::new_err(format!("无法序列化命令: {e}")))
            .map(Bound::unbind)
    }

    /// 获取账户模式
    #[pyo3(signature = (account_id, extra=None, generate=false))]
    fn get_account_mode(
        &self,
        py: Python,
        account_id: usize,
        extra: Option<&Bound<'_, PyDict>>,
        generate: bool,
    ) -> PyResult<Py<PyAny>> {
        let extra: HashMap<String, Value> =
            extra.map(|e| depythonize(e).unwrap()).unwrap_or_default();

        let params = GetAccountModeParams { extra };

        let cmd = Command::Sync(SyncCommand::GetAccountModeExt(params));

        let cmd = ExecutionCommand {
            account_id,
            context: Context::default(),
            cmd,
        };

        if generate {
            return pythonize(py, &cmd.to_flatten())
                .map(Bound::unbind)
                .map_err(|e| PyTypeError::new_err(format!("无法序列化命令: {e}")));
        }

        let v = py.allow_threads(move || {
            self.0
                .handle
                .block_on(async move { self.0.handle_cmd(cmd).await })
        });
        v.map(|v| pythonize(py, &v))
            .map_err(|e| PyErr::new::<PyAny, _>(e.to_string()))?
            .map_err(|e| PyTypeError::new_err(format!("无法序列化命令: {e}")))
            .map(Bound::unbind)
    }

    /// 设置账户模式
    #[pyo3(signature = (account_id, account_mode, extra=None, generate=false))]
    fn set_account_mode(
        &self,
        py: Python,
        account_id: usize,
        account_mode: Bound<'_, PyAny>,
        extra: Option<&Bound<'_, PyDict>>,
        generate: bool,
    ) -> PyResult<Py<PyAny>> {
        let extra: HashMap<String, Value> =
            extra.map(|e| depythonize(e).unwrap()).unwrap_or_default();

        let account_mode: AccountMode = depythonize(&account_mode)
            .map_err(|e| PyTypeError::new_err(format!("无法解析账户模式: {e}")))?;

        let params = SetAccountModeParams {
            mode: account_mode,
            extra,
        };

        let cmd = Command::Sync(SyncCommand::SetAccountModeExt(params));

        let cmd = ExecutionCommand {
            account_id,
            context: Context::default(),
            cmd,
        };

        if generate {
            return pythonize(py, &cmd.to_flatten())
                .map(Bound::unbind)
                .map_err(|e| PyTypeError::new_err(format!("无法序列化命令: {e}")));
        }

        let v = py.allow_threads(move || {
            self.0
                .handle
                .block_on(async move { self.0.handle_cmd(cmd).await })
        });
        v.map(|v| pythonize(py, &v))
            .map_err(|e| PyErr::new::<PyAny, _>(e.to_string()))?
            .map_err(|e| PyTypeError::new_err(format!("无法序列化命令: {e}")))
            .map(Bound::unbind)
    }

    /// 获取用户ID
    #[pyo3(signature = (account_id, extra=None, generate=false))]
    fn get_user_id(
        &self,
        py: Python,
        account_id: usize,
        extra: Option<&Bound<'_, PyDict>>,
        generate: bool,
    ) -> PyResult<Py<PyAny>> {
        let extra: HashMap<String, Value> =
            extra.map(|e| depythonize(e).unwrap()).unwrap_or_default();

        let params = GetUserIdParams { extra };

        let cmd = Command::Sync(SyncCommand::GetUserIdExt(params));

        let cmd = ExecutionCommand {
            account_id,
            context: Context::default(),
            cmd,
        };

        if generate {
            return pythonize(py, &cmd.to_flatten())
                .map(Bound::unbind)
                .map_err(|e| PyTypeError::new_err(format!("无法序列化命令: {e}")));
        }

        let v = py.allow_threads(move || {
            self.0
                .handle
                .block_on(async move { self.0.handle_cmd(cmd).await })
        });
        v.map(|v| pythonize(py, &v))
            .map_err(|e| PyErr::new::<PyAny, _>(e.to_string()))?
            .map_err(|e| PyTypeError::new_err(format!("无法序列化命令: {e}")))
            .map(Bound::unbind)
    }

    /// 借币
    #[pyo3(signature = (account_id, coin, amount, extra=None, generate=false))]
    fn borrow(
        &self,
        py: Python,
        account_id: usize,
        coin: &str,
        amount: f64,
        extra: Option<&Bound<'_, PyDict>>,
        generate: bool,
    ) -> PyResult<Py<PyAny>> {
        let extra: HashMap<String, Value> =
            extra.map(|e| depythonize(e).unwrap()).unwrap_or_default();

        let params = BorrowCoinParams {
            coin: coin.to_string(),
            amount,
            extra,
        };

        let cmd = Command::Sync(SyncCommand::BorrowExt(params));

        let cmd = ExecutionCommand {
            account_id,
            context: Context::default(),
            cmd,
        };

        if generate {
            return pythonize(py, &cmd.to_flatten())
                .map(Bound::unbind)
                .map_err(|e| PyTypeError::new_err(format!("无法序列化命令: {e}")));
        }

        let v = py.allow_threads(move || {
            self.0
                .handle
                .block_on(async move { self.0.handle_cmd(cmd).await })
        });
        v.map(|v| pythonize(py, &v))
            .map_err(|e| PyErr::new::<PyAny, _>(e.to_string()))?
            .map_err(|e| PyTypeError::new_err(format!("无法序列化命令: {e}")))
            .map(Bound::unbind)
    }

    /// 查询借币
    #[pyo3(signature = (account_id, coin=None, extra=None, generate=false))]
    fn get_borrowed(
        &self,
        py: Python,
        account_id: usize,
        coin: Option<&str>,
        extra: Option<&Bound<'_, PyDict>>,
        generate: bool,
    ) -> PyResult<Py<PyAny>> {
        let extra: HashMap<String, Value> =
            extra.map(|e| depythonize(e).unwrap()).unwrap_or_default();

        let params = GetBorrowParams {
            coin: coin.map(|s| s.to_string()),
            extra,
        };

        let cmd = Command::Sync(SyncCommand::GetBorrowedExt(params));

        let cmd = ExecutionCommand {
            account_id,
            context: Context::default(),
            cmd,
        };

        if generate {
            return pythonize(py, &cmd.to_flatten())
                .map(Bound::unbind)
                .map_err(|e| PyTypeError::new_err(format!("无法序列化命令: {e}")));
        }

        let v = py.allow_threads(move || {
            self.0
                .handle
                .block_on(async move { self.0.handle_cmd(cmd).await })
        });
        v.map(|v| pythonize(py, &v))
            .map_err(|e| PyErr::new::<PyAny, _>(e.to_string()))?
            .map_err(|e| PyTypeError::new_err(format!("无法序列化命令: {e}")))
            .map(Bound::unbind)
    }

    /// 还币
    #[pyo3(signature = (account_id, coin, amount, extra=None, generate=false))]
    fn repay(
        &self,
        py: Python,
        account_id: usize,
        coin: &str,
        amount: f64,
        extra: Option<&Bound<'_, PyDict>>,
        generate: bool,
    ) -> PyResult<Py<PyAny>> {
        let extra: HashMap<String, Value> =
            extra.map(|e| depythonize(e).unwrap()).unwrap_or_default();

        let params = RepayCoinParams {
            coin: coin.to_string(),
            amount,
            extra,
        };

        let cmd = Command::Sync(SyncCommand::RepayExt(params));

        let cmd = ExecutionCommand {
            account_id,
            context: Context::default(),
            cmd,
        };

        if generate {
            return pythonize(py, &cmd.to_flatten())
                .map(Bound::unbind)
                .map_err(|e| PyTypeError::new_err(format!("无法序列化命令: {e}")));
        }

        let v = py.allow_threads(move || {
            self.0
                .handle
                .block_on(async move { self.0.handle_cmd(cmd).await })
        });
        v.map(|v| pythonize(py, &v))
            .map_err(|e| PyErr::new::<PyAny, _>(e.to_string()))?
            .map_err(|e| PyTypeError::new_err(format!("无法序列化命令: {e}")))
            .map(Bound::unbind)
    }

    /// 获取借贷利率
    #[pyo3(signature = (account_id, coin=None, extra=None, generate=false))]
    fn get_borrow_rate(
        &self,
        py: Python,
        account_id: usize,
        coin: Option<&str>,
        extra: Option<&Bound<'_, PyDict>>,
        generate: bool,
    ) -> PyResult<Py<PyAny>> {
        let extra: HashMap<String, Value> =
            extra.map(|e| depythonize(e).unwrap()).unwrap_or_default();

        let params = GetBorrowRateParams {
            coin: coin.map(|s| s.to_string()),
            extra,
        };

        let cmd = Command::Sync(SyncCommand::GetBorrowRateExt(params));

        let cmd = ExecutionCommand {
            account_id,
            context: Context::default(),
            cmd,
        };

        if generate {
            return pythonize(py, &cmd.to_flatten())
                .map(Bound::unbind)
                .map_err(|e| PyTypeError::new_err(format!("无法序列化命令: {e}")));
        }

        let v = py.allow_threads(move || {
            self.0
                .handle
                .block_on(async move { self.0.handle_cmd(cmd).await })
        });
        v.map(|v| pythonize(py, &v))
            .map_err(|e| PyErr::new::<PyAny, _>(e.to_string()))?
            .map_err(|e| PyTypeError::new_err(format!("无法序列化命令: {e}")))
            .map(Bound::unbind)
    }

    /// 获取借贷限额
    #[pyo3(signature = (account_id, coin, is_vip=None, extra=None, generate=false))]
    fn get_borrow_limit(
        &self,
        py: Python,
        account_id: usize,
        coin: &str,
        is_vip: Option<bool>,
        extra: Option<&Bound<'_, PyDict>>,
        generate: bool,
    ) -> PyResult<Py<PyAny>> {
        let extra: HashMap<String, Value> =
            extra.map(|e| depythonize(e).unwrap()).unwrap_or_default();

        let params = GetBorrowLimitsParams {
            is_vip,
            coin: coin.to_string(),
            extra,
        };

        let cmd = Command::Sync(SyncCommand::GetBorrowLimitExt(params));

        let cmd = ExecutionCommand {
            account_id,
            context: Context::default(),
            cmd,
        };

        if generate {
            return pythonize(py, &cmd.to_flatten())
                .map(Bound::unbind)
                .map_err(|e| PyTypeError::new_err(format!("无法序列化命令: {e}")));
        }

        let v = py.allow_threads(move || {
            self.0
                .handle
                .block_on(async move { self.0.handle_cmd(cmd).await })
        });
        v.map(|v| pythonize(py, &v))
            .map_err(|e| PyErr::new::<PyAny, _>(e.to_string()))?
            .map_err(|e| PyTypeError::new_err(format!("无法序列化命令: {e}")))
            .map(Bound::unbind)
    }

    /// 查询最佳买卖价（单个交易对）
    #[pyo3(signature = (account_id, symbol, extra=None, generate=false))]
    fn get_bbo(
        &self,
        py: Python,
        account_id: usize,
        symbol: &str,
        extra: Option<&Bound<'_, PyDict>>,
        generate: bool,
    ) -> PyResult<Py<PyAny>> {
        let extra: HashMap<String, Value> =
            extra.map(|e| depythonize(e).unwrap()).unwrap_or_default();

        let symbol = serde_plain::from_str(symbol)
            .map_err(|e| PyTypeError::new_err(format!("无法解析symbol: {e}")))?;

        let params = GetBboTickerParams { symbol, extra };

        let cmd = Command::Sync(SyncCommand::BboExt(params));

        let cmd = ExecutionCommand {
            account_id,
            context: Context::default(),
            cmd,
        };

        if generate {
            return pythonize(py, &cmd.to_flatten())
                .map(Bound::unbind)
                .map_err(|e| PyTypeError::new_err(format!("无法序列化命令: {e}")));
        }

        let v = py.allow_threads(move || {
            self.0
                .handle
                .block_on(async move { self.0.handle_cmd(cmd).await })
        });
        v.map(|v| pythonize(py, &v))
            .map_err(|e| PyErr::new::<PyAny, _>(e.to_string()))?
            .map_err(|e| PyTypeError::new_err(format!("无法序列化命令: {e}")))
            .map(Bound::unbind)
    }

    /// 查询所有交易对的最佳买卖价
    #[pyo3(signature = (account_id, extra=None, generate=false))]
    fn get_bbo_tickers(
        &self,
        py: Python,
        account_id: usize,
        extra: Option<&Bound<'_, PyDict>>,
        generate: bool,
    ) -> PyResult<Py<PyAny>> {
        let extra: HashMap<String, Value> =
            extra.map(|e| depythonize(e).unwrap()).unwrap_or_default();

        let params = GetBboTickersParams { extra };

        let cmd = Command::Sync(SyncCommand::BboTickersExt(params));

        let cmd = ExecutionCommand {
            account_id,
            context: Context::default(),
            cmd,
        };

        if generate {
            return pythonize(py, &cmd.to_flatten())
                .map(Bound::unbind)
                .map_err(|e| PyTypeError::new_err(format!("无法序列化命令: {e}")));
        }

        let v = py.allow_threads(move || {
            self.0
                .handle
                .block_on(async move { self.0.handle_cmd(cmd).await })
        });
        v.map(|v| pythonize(py, &v))
            .map_err(|e| PyErr::new::<PyAny, _>(e.to_string()))?
            .map_err(|e| PyTypeError::new_err(format!("无法序列化命令: {e}")))
            .map(Bound::unbind)
    }

    /// 查询深度
    #[pyo3(signature = (account_id, symbol, limit=None, extra=None, generate=false))]
    fn get_depth(
        &self,
        py: Python,
        account_id: usize,
        symbol: &str,
        limit: Option<u32>,
        extra: Option<&Bound<'_, PyDict>>,
        generate: bool,
    ) -> PyResult<Py<PyAny>> {
        let extra: HashMap<String, Value> =
            extra.map(|e| depythonize(e).unwrap()).unwrap_or_default();
        let symbol = serde_plain::from_str(symbol)
            .map_err(|e| PyTypeError::new_err(format!("无法解析symbol: {e}")))?;

        let params = GetDepthParams {
            symbol,
            limit,
            extra,
        };

        let cmd = Command::Sync(SyncCommand::DepthExt(params));

        let cmd = ExecutionCommand {
            account_id,
            context: Context::default(),
            cmd,
        };

        if generate {
            return pythonize(py, &cmd.to_flatten())
                .map(Bound::unbind)
                .map_err(|e| PyTypeError::new_err(format!("无法序列化命令: {e}")));
        }

        let v = py.allow_threads(move || {
            self.0
                .handle
                .block_on(async move { self.0.handle_cmd(cmd).await })
        });
        v.map(|v| pythonize(py, &v))
            .map_err(|e| PyErr::new::<PyAny, _>(e.to_string()))?
            .map_err(|e| PyTypeError::new_err(format!("无法序列化命令: {e}")))
            .map(Bound::unbind)
    }

    /// 查询产品信息
    #[pyo3(signature = (account_id, symbol, extra=None, generate=false))]
    fn get_instrument(
        &self,
        py: Python,
        account_id: usize,
        symbol: &str,
        extra: Option<&Bound<'_, PyDict>>,
        generate: bool,
    ) -> PyResult<Py<PyAny>> {
        let extra: HashMap<String, Value> =
            extra.map(|e| depythonize(e).unwrap()).unwrap_or_default();

        let symbol = serde_plain::from_str(symbol)
            .map_err(|e| PyTypeError::new_err(format!("无法解析symbol: {e}")))?;

        let params = GetInstrumentParams { symbol, extra };

        let cmd = Command::Sync(SyncCommand::InstrumentExt(params));

        let cmd = ExecutionCommand {
            account_id,
            context: Context::default(),
            cmd,
        };

        if generate {
            return pythonize(py, &cmd.to_flatten())
                .map(Bound::unbind)
                .map_err(|e| PyTypeError::new_err(format!("无法序列化命令: {e}")));
        }

        let v = py.allow_threads(move || {
            self.0
                .handle
                .block_on(async move { self.0.handle_cmd(cmd).await })
        });
        v.map(|v| pythonize(py, &v))
            .map_err(|e| PyErr::new::<PyAny, _>(e.to_string()))?
            .map_err(|e| PyTypeError::new_err(format!("无法序列化命令: {e}")))
            .map(Bound::unbind)
    }

    /// 查询所有产品信息
    #[pyo3(signature = (account_id, extra=None, generate=false))]
    fn get_instruments(
        &self,
        py: Python,
        account_id: usize,
        extra: Option<&Bound<'_, PyDict>>,
        generate: bool,
    ) -> PyResult<Py<PyAny>> {
        let extra: HashMap<String, Value> =
            extra.map(|e| depythonize(e).unwrap()).unwrap_or_default();

        let params = GetInstrumentsParams { extra };

        let cmd = Command::Sync(SyncCommand::InstrumentsExt(params));

        let cmd = ExecutionCommand {
            account_id,
            context: Context::default(),
            cmd,
        };

        if generate {
            return pythonize(py, &cmd.to_flatten())
                .map(Bound::unbind)
                .map_err(|e| PyTypeError::new_err(format!("无法序列化命令: {e}")));
        }

        let v = py.allow_threads(move || {
            self.0
                .handle
                .block_on(async move { self.0.handle_cmd(cmd).await })
        });
        v.map(|v| pythonize(py, &v))
            .map_err(|e| PyErr::new::<PyAny, _>(e.to_string()))?
            .map_err(|e| PyTypeError::new_err(format!("无法序列化命令: {e}")))
            .map(Bound::unbind)
    }

    /// 查询资金费率
    #[pyo3(signature = (account_id, extra=None, generate=false))]
    fn get_funding_rates(
        &self,
        py: Python,
        account_id: usize,
        extra: Option<&Bound<'_, PyDict>>,
        generate: bool,
    ) -> PyResult<Py<PyAny>> {
        let extra: HashMap<String, Value> =
            extra.map(|e| depythonize(e).unwrap()).unwrap_or_default();

        let params = GetFundingRatesParams { extra };

        let cmd = Command::Sync(SyncCommand::FundingRateExt(params));

        let cmd = ExecutionCommand {
            account_id,
            context: Context::default(),
            cmd,
        };

        if generate {
            return pythonize(py, &cmd.to_flatten())
                .map(Bound::unbind)
                .map_err(|e| PyTypeError::new_err(format!("无法序列化命令: {e}")));
        }

        let v = py.allow_threads(move || {
            self.0
                .handle
                .block_on(async move { self.0.handle_cmd(cmd).await })
        });
        v.map(|v| pythonize(py, &v))
            .map_err(|e| PyErr::new::<PyAny, _>(e.to_string()))?
            .map_err(|e| PyTypeError::new_err(format!("无法序列化命令: {e}")))
            .map(Bound::unbind)
    }

    /// 查询单个交易对资金费率
    #[pyo3(signature = (account_id, symbol, extra=None, generate=false))]
    fn get_funding_rate_by_symbol(
        &self,
        py: Python,
        account_id: usize,
        symbol: &str,
        extra: Option<&Bound<'_, PyDict>>,
        generate: bool,
    ) -> PyResult<Py<PyAny>> {
        let extra: HashMap<String, Value> =
            extra.map(|e| depythonize(e).unwrap()).unwrap_or_default();
        let symbol = serde_plain::from_str(symbol)
            .map_err(|e| PyTypeError::new_err(format!("无法解析symbol: {e}")))?;

        let params = GetFundingRateParams { symbol, extra };

        let cmd = Command::Sync(SyncCommand::FundingRateBySymbolExt(params));

        let cmd = ExecutionCommand {
            account_id,
            context: Context::default(),
            cmd,
        };

        if generate {
            return pythonize(py, &cmd.to_flatten())
                .map(Bound::unbind)
                .map_err(|e| PyTypeError::new_err(format!("无法序列化命令: {e}")));
        }

        let v = py.allow_threads(move || {
            self.0
                .handle
                .block_on(async move { self.0.handle_cmd(cmd).await })
        });
        v.map(|v| pythonize(py, &v))
            .map_err(|e| PyErr::new::<PyAny, _>(e.to_string()))?
            .map_err(|e| PyTypeError::new_err(format!("无法序列化命令: {e}")))
            .map(Bound::unbind)
    }

    /// 获取资金费率历史记录
    #[pyo3(signature = (account_id, symbol=None, since_secs=None, limit=100, extra=None, generate=false))]
    #[allow(clippy::too_many_arguments)]
    fn get_funding_rate_history(
        &self,
        py: Python,
        account_id: usize,
        symbol: Option<&str>,
        since_secs: Option<i64>,
        limit: Option<u32>,
        extra: Option<&Bound<'_, PyDict>>,
        generate: bool,
    ) -> PyResult<Py<PyAny>> {
        let extra: HashMap<String, Value> =
            extra.map(|e| depythonize(e).unwrap()).unwrap_or_default();

        let symbol = if let Some(s) = symbol {
            Some(
                serde_plain::from_str(s)
                    .map_err(|e| PyTypeError::new_err(format!("无法解析symbol: {e}")))?,
            )
        } else {
            None
        };

        let params = GetFundingRateHistoryParams {
            symbol,
            since_secs,
            limit: limit.unwrap_or(100),
            extra,
        };

        let cmd = Command::Sync(SyncCommand::FundingRateHistory(params));

        let cmd = ExecutionCommand {
            account_id,
            context: Context::default(),
            cmd,
        };

        if generate {
            return pythonize(py, &cmd.to_flatten())
                .map(Bound::unbind)
                .map_err(|e| PyTypeError::new_err(format!("无法序列化命令: {e}")));
        }

        let v = py.allow_threads(move || {
            self.0
                .handle
                .block_on(async move { self.0.handle_cmd(cmd).await })
        });
        v.map(|v| pythonize(py, &v))
            .map_err(|e| PyErr::new::<PyAny, _>(e.to_string()))?
            .map_err(|e| PyTypeError::new_err(format!("无法序列化命令: {e}")))
            .map(Bound::unbind)
    }

    /// 获取标记价格
    #[pyo3(signature = (account_id, symbol=None, extra=None, generate=false))]
    fn get_mark_price(
        &self,
        py: Python,
        account_id: usize,
        symbol: Option<&str>,
        extra: Option<&Bound<'_, PyDict>>,
        generate: bool,
    ) -> PyResult<Py<PyAny>> {
        let extra: HashMap<String, Value> =
            extra.map(|e| depythonize(e).unwrap()).unwrap_or_default();

        let symbol = if let Some(s) = symbol {
            Some(
                serde_plain::from_str(s)
                    .map_err(|e| PyTypeError::new_err(format!("无法解析symbol: {e}")))?,
            )
        } else {
            None
        };

        let params = GetMarkPriceParams { symbol, extra };

        let cmd = Command::Sync(SyncCommand::MarkPriceExt(params));

        let cmd = ExecutionCommand {
            account_id,
            context: Context::default(),
            cmd,
        };

        if generate {
            return pythonize(py, &cmd.to_flatten())
                .map(Bound::unbind)
                .map_err(|e| PyTypeError::new_err(format!("无法序列化命令: {e}")));
        }

        let v = py.allow_threads(move || {
            self.0
                .handle
                .block_on(async move { self.0.handle_cmd(cmd).await })
        });
        v.map(|v| pythonize(py, &v))
            .map_err(|e| PyErr::new::<PyAny, _>(e.to_string()))?
            .map_err(|e| PyTypeError::new_err(format!("无法序列化命令: {e}")))
            .map(Bound::unbind)
    }

    /// 批量取消根据订单ID
    #[allow(clippy::too_many_arguments)]
    #[pyo3(signature = (account_id, symbol=None, order_ids=None, client_order_ids=None, extra=None, sync=true, generate=false))]
    fn batch_cancel_order_by_id(
        &self,
        py: Python,
        account_id: usize,
        symbol: Option<&str>,
        order_ids: Option<Vec<String>>,
        client_order_ids: Option<Vec<String>>,
        extra: Option<&Bound<'_, PyDict>>,
        sync: bool,
        generate: bool,
    ) -> PyResult<Py<PyAny>> {
        let extra: HashMap<String, Value> =
            extra.map(|e| depythonize(e).unwrap()).unwrap_or_default();

        let symbol = if let Some(s) = symbol {
            Some(
                serde_plain::from_str(s)
                    .map_err(|e| PyTypeError::new_err(format!("无法解析symbol: {e}")))?,
            )
        } else {
            None
        };

        let params = BatchCancelOrderByIdsParams {
            symbol,
            ids: order_ids,
            cids: client_order_ids,
            extra,
        };

        let cmd = if sync {
            Command::Sync(SyncCommand::BatchCancelOrderByIdExt(params))
        } else {
            Command::Async(AsyncCommand::BatchCancelOrderByIdExt(params))
        };

        let cmd = ExecutionCommand {
            account_id,
            context: Context::default(),
            cmd,
        };

        if generate {
            return pythonize(py, &cmd.to_flatten())
                .map(Bound::unbind)
                .map_err(|e| PyTypeError::new_err(format!("无法序列化命令: {e}")));
        }

        let v = py.allow_threads(move || {
            self.0
                .handle
                .block_on(async move { self.0.handle_cmd(cmd).await })
        });
        v.map(|v| pythonize(py, &v))
            .map_err(|e| PyErr::new::<PyAny, _>(e.to_string()))?
            .map_err(|e| PyTypeError::new_err(format!("无法序列化命令: {e}")))
            .map(Bound::unbind)
    }

    /// 下止损订单
    #[allow(clippy::too_many_arguments)]
    #[pyo3(signature = (account_id, symbol, pos_side, take_profit=None, stop_loss=None, is_dual_side=false, extra=None, generate=false))]
    fn post_stop_order(
        &self,
        py: Python,
        account_id: usize,
        symbol: &str,
        pos_side: &str,
        take_profit: Option<&Bound<'_, PyDict>>,
        stop_loss: Option<&Bound<'_, PyDict>>,
        is_dual_side: bool,
        extra: Option<&Bound<'_, PyDict>>,
        generate: bool,
    ) -> PyResult<Py<PyAny>> {
        let extra: HashMap<String, Value> =
            extra.map(|e| depythonize(e).unwrap()).unwrap_or_default();
        let symbol = serde_plain::from_str(symbol)
            .map_err(|e| PyTypeError::new_err(format!("无法解析symbol: {e}")))?;
        let pos_side = serde_plain::from_str(pos_side)
            .map_err(|e| PyTypeError::new_err(format!("无法解析pos_side: {e}")))?;
        let take_profit = if let Some(tp) = take_profit {
            Some(depythonize(tp)?)
        } else {
            None
        };
        let stop_loss = if let Some(sl) = stop_loss {
            Some(depythonize(sl)?)
        } else {
            None
        };

        let cmd = PostStopOrderParams {
            symbol,
            pos_side,
            take_profit,
            stop_loss,
            is_dual_side,
            extra,
        };

        let cmd = Command::Sync(SyncCommand::PostStopOrder(cmd));

        let cmd = ExecutionCommand {
            account_id,
            context: Context::default(),
            cmd,
        };

        if generate {
            return pythonize(py, &cmd.to_flatten())
                .map(Bound::unbind)
                .map_err(|e| PyTypeError::new_err(format!("无法序列化命令: {e}")));
        }

        let v = py.allow_threads(move || {
            self.0
                .handle
                .block_on(async move { self.0.handle_cmd(cmd).await })
        });
        v.map(|v| pythonize(py, &v))
            .map_err(|e| PyErr::new::<PyAny, _>(e.to_string()))?
            .map_err(|e| PyTypeError::new_err(format!("无法序列化命令: {e}")))
            .map(Bound::unbind)
    }

    /// 获取止损订单列表
    #[allow(clippy::too_many_arguments)]
    #[pyo3(signature = (account_id, symbol, limit=None, id=None, cid=None, extra=None, generate=false))]
    fn get_stop_orders(
        &self,
        py: Python,
        account_id: usize,
        symbol: &str,
        limit: Option<u32>,
        id: Option<String>,
        cid: Option<String>,
        extra: Option<&Bound<'_, PyDict>>,
        generate: bool,
    ) -> PyResult<Py<PyAny>> {
        let extra: HashMap<String, Value> =
            extra.map(|e| depythonize(e).unwrap()).unwrap_or_default();
        let symbol = serde_plain::from_str(symbol)
            .map_err(|e| PyTypeError::new_err(format!("无法解析symbol: {e}")))?;
        let order_id = if let Some(id) = id {
            Some(OrderId::Id(id))
        } else {
            cid.map(OrderId::ClientOrderId)
        };

        let cmd = SyncCommand::GetStopOrders(GetStopOrdersParams {
            symbol,
            limit,
            order_id,
            extra,
        });

        let cmd = ExecutionCommand {
            account_id,
            cmd: Command::Sync(cmd),
            context: Context::default(),
        };

        if generate {
            return pythonize(py, &cmd.to_flatten())
                .map(Bound::unbind)
                .map_err(|e| PyTypeError::new_err(format!("无法序列化命令: {e}")));
        }

        let v = py.allow_threads(move || {
            self.0
                .handle
                .block_on(async move { self.0.handle_cmd(cmd).await })
        });
        v.map(|v| pythonize(py, &v))
            .map_err(|e| PyErr::new::<PyAny, _>(e.to_string()))?
            .map_err(|e| PyTypeError::new_err(format!("无法序列化命令: {e}")))
            .map(Bound::unbind)
    }

    #[allow(clippy::too_many_arguments)]
    /// 修改止损订单
    #[pyo3(signature = (account_id, symbol, id=None, cid=None, take_profit=None, stop_loss=None, extra=None, generate=false))]
    fn amend_stop_order(
        &self,
        py: Python,
        account_id: usize,
        symbol: &str,
        id: Option<String>,
        cid: Option<String>,
        take_profit: Option<&Bound<'_, PyDict>>,
        stop_loss: Option<&Bound<'_, PyDict>>,
        extra: Option<&Bound<'_, PyDict>>,
        generate: bool,
    ) -> PyResult<Py<PyAny>> {
        let extra: HashMap<String, Value> =
            extra.map(|e| depythonize(e).unwrap()).unwrap_or_default();
        let symbol = serde_plain::from_str(symbol)
            .map_err(|e| PyTypeError::new_err(format!("无法解析symbol: {e}")))?;
        let order_id = if let Some(id) = id {
            OrderId::Id(id)
        } else if let Some(cid) = cid {
            OrderId::ClientOrderId(cid)
        } else {
            return Err(PyTypeError::new_err(
                "修改止盈止损单订单id 和 cid 不能同时为空",
            ));
        };

        let take_profit = if let Some(tp) = take_profit {
            Some(depythonize(tp)?)
        } else {
            None
        };
        let stop_loss = if let Some(sl) = stop_loss {
            Some(depythonize(sl)?)
        } else {
            None
        };

        let cmd = Command::Sync(SyncCommand::AmendStopOrder(AmendStopOrderParams {
            symbol,
            order_id,
            take_profit,
            stop_loss,
            extra,
        }));

        let cmd = ExecutionCommand {
            account_id,
            context: Context::default(),
            cmd,
        };

        if generate {
            return pythonize(py, &cmd.to_flatten())
                .map(Bound::unbind)
                .map_err(|e| PyTypeError::new_err(format!("无法序列化命令: {e}")));
        }

        let v = py.allow_threads(move || {
            self.0
                .handle
                .block_on(async move { self.0.handle_cmd(cmd).await })
        });
        v.map(|v| pythonize(py, &v))
            .map_err(|e| PyErr::new::<PyAny, _>(e.to_string()))?
            .map_err(|e| PyTypeError::new_err(format!("无法序列化命令: {e}")))
            .map(Bound::unbind)
    }

    /// 取消止损订单
    #[allow(clippy::too_many_arguments)]
    #[pyo3(signature = (account_id, symbol, id=None, cid=None, extra=None, generate=false))]
    fn cancel_stop_order(
        &self,
        py: Python,
        account_id: usize,
        symbol: &str,
        id: Option<String>,
        cid: Option<String>,
        extra: Option<&Bound<'_, PyDict>>,
        generate: bool,
    ) -> PyResult<Py<PyAny>> {
        let extra: HashMap<String, Value> =
            extra.map(|e| depythonize(e).unwrap()).unwrap_or_default();
        let symbol = serde_plain::from_str(symbol)
            .map_err(|e| PyTypeError::new_err(format!("无法解析symbol: {e}")))?;
        let order_id = if let Some(id) = id {
            OrderId::Id(id)
        } else if let Some(cid) = cid {
            OrderId::ClientOrderId(cid)
        } else {
            return Err(PyTypeError::new_err("order_id 和 cid 不能同时为空"));
        };

        let cmd = Command::Sync(SyncCommand::CancelStopOrder(CancelStopOrderParams {
            symbol,
            order_id,
            extra,
        }));

        let cmd = ExecutionCommand {
            account_id,
            context: Context::default(),
            cmd,
        };

        if generate {
            return pythonize(py, &cmd.to_flatten())
                .map(Bound::unbind)
                .map_err(|e| PyTypeError::new_err(format!("无法序列化命令: {e}")));
        }

        let v = py.allow_threads(move || {
            self.0
                .handle
                .block_on(async move { self.0.handle_cmd(cmd).await })
        });
        v.map(|v| pythonize(py, &v))
            .map_err(|e| PyErr::new::<PyAny, _>(e.to_string()))?
            .map_err(|e| PyTypeError::new_err(format!("无法序列化命令: {e}")))
            .map(Bound::unbind)
    }
}

/// Python模块定义
///
/// 注册Trader类和其他工具函数到Python模块中
#[pymodule]
pub fn traderv2(m: &Bound<'_, PyModule>) -> PyResult<()> {
    // 注册Trader类
    m.add_class::<TraderV2>()?;
    // 注册创建CID的函数
    m.add_function(wrap_pyfunction!(create_cid, m)?)?;
    Ok(())
}
