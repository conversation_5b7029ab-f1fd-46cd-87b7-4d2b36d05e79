use crate::web::client::WebClient;
use quant_common::{Result, qerror};
use std::{sync::Arc, time::Duration};

pub mod client;

pub struct WebServer {
    pub client: Arc<WebClient>,
}

impl WebServer {
    pub fn new(client: Arc<WebClient>) -> Self {
        Self { client }
    }

    pub async fn validate(&self) -> Result<()> {
        return Ok(());
        // report server info
        self.client.update_server_internal().await?;
        info!("上传服务器信息成功");

        // validate server
        let response = self.client.is_auth_internal().await?;
        if !response.is_authorized {
            return Err(qerror!("服务器未授权, 请前往open quant网页端授权"));
        } else {
            info!("服务器已授权");
        }

        Ok(())
    }

    pub async fn loop_report(&self) -> Result<()> {
        let client = self.client.clone();
        tokio::spawn(async move {
            loop {
                // report server info
                if let Err(e) = client.update_server_internal().await {
                    tracing::error!("上传服务器信息失败: {}", e);
                }
                tokio::time::sleep(Duration::from_secs(300)).await;
            }
        });

        Ok(())
    }
}
