# Changelog

## [0.9.0-alpha.13] - 2025-07-08

### 🐛 问题修复
- [BitgetSwap] 支持adl相关的订单tradeSide
- [GateSwap] funding rate 最小最大值采用v2版本接口
- [GateSwap] 修复减仓单数量处理错误问题
- [BybitSwap] 修复获取余额报错问题、修复下单量问题

### ✨ 功能新增
- 新增止赢止损订单相关接口 目前支持交易所: BinanceSwap、BitgetSwap、GateSwap
  - post_stop_order
  - get_stop_orders
  - amend_stop_order
  - cancel_stop_order



## [0.9.0-alpha.12] - 2025-07-03

### 💥 不兼容改动
- 正式网也必须配置密钥才能运行

### 🐛 问题修复
- start/subscribe 过程中按下ctrl c自动退出
- coinex修复交易币对解析错误
- 修复request 接口query & body参数解析错误
- 修复 return cmds不包含logs报错问题

### ✨ 功能新增
- 仓位增加爆仓价、保证金率、标记价格等字段详见文档[7.2 Position（持仓）数据结构]
- 文档新增http_request & request 接口示例
- [HyperLiquidSwap] 新增支持get_funding_fee 接口

### 📝 文档更新
- **新增Position（持仓）数据结构说明**：
  - 在"七、数据结构说明"章节新增完整的Position数据结构文档
- **完善持仓相关API文档**：
  - 更新`get_position`和`get_positions`方法的返回值说明


## [0.9.0-alpha.11] - 2025-07-01

### 🐛 问题修复
- 修复python return 错误的cmd跳过而不报错的问题
- 修复get funding rates报错问题
- [KucoinSwap] 优化资金费率接口
- [GateSwap] 修复统一账户获取仓位报错



## [0.9.0-alpha.10] - 2025-06-30

### 🐛 问题修复
- [BinanceSwap] 修复get_instrument 接口返回的新类型报错
- [GateSwap] 支持get_funding_rate_history 接口



## [0.9.0-alpha.9] - 2025-06-27

### 🐛 问题修复
- [BybitSwap] 修复大部分问题，优化性能\
- [BinanceSwap] 修复双向持仓下的问题
- 修复交割合约symbol解析问题

### 🔧 优化改进
- 加上编译优化，优化性能
- 优化下发多条命令的系统延迟



## [0.9.0-alpha.8] - 2025-06-25

### 🐛 问题修复
- [BybitSwap] 修复订单postonly不支持的错误

### ✨ 功能新增
- [BybitSwap] 新增支持order and fill订阅
- Okx支持币本位合约(OkxSwapUsd)、交割合约(OkxFuture)
- Binance支持交割合约(BinanceFuture)



## [0.9.0-alpha.7] - 2025-06-24

### 🐛 问题修复
- [BybitSwap] 修复余额获取逻辑完善所有字段
- [HyperLiquidSwap] 修复部分接口报错问题

### ✨ 功能新增
- TimerSubscribe结构体新增`initial_delay`字段，支持设置定时器启动前的初始延迟时间
- 通过`initial_delay`可以错开多个定时器的启动时间，避免资源竞争和并发冲突
- 定时器订阅现在支持更精细的启动时间控制，提升多定时器环境下的系统稳定性
- 新增`get_funding_rate_history`接口，支持查询历史资金费率记录
  - 支持按交易对和时间范围查询历史资金费率
  - 支持获取单个或全部交易对的资金费率历史
  - 提供灵活的时间范围筛选和数量限制功能
- 新增`get_kline`接口，支持获取K线数据
  - 支持多种时间粒度：1m, 3m, 5m, 15m, 30m, 1h, 2h, 4h, 6h, 8h, 12h, 1d, 3d, 1w, 1M
  - 支持指定时间范围和数量限制
  - K线数据包含完整的OHLCV信息和可选的成交笔数、主动买入量等字段
- 资金费率结构体功能增强
  - 新增`min_funding_rate`字段：资金费率最小值（交易所设定的下限）
  - 新增`max_funding_rate`字段：资金费率最大值（交易所设定的上限）
- WebSocket新增Kline数据订阅
  - 支持实时K线数据推送，可订阅多个交易对和不同时间周期
  - 通过`on_kline`回调接收实时K线更新
  - K线数据包含`confirm`字段标识K线是否已完结，便于技术分析

### 📝 文档更新
- 完善定时器订阅相关文档，新增`initial_delay`字段的配置说明和使用场景
- 新增初始延迟使用场景对比表格，提供不同场景下的建议延迟时间
- 更新所有定时器订阅示例，展示如何合理配置初始延迟避免资源竞争
- 完善资金费率相关API文档，添加新增字段的详细说明和使用示例
- 新增K线数据获取和订阅的完整使用指南
- 更新WebSocket订阅配置，添加Kline订阅示例
- 补充历史资金费率查询的实际应用场景和最佳实践


## [0.9.0-alpha.6] - 2025-06-18

### 💥 不兼容改动
- 移除了`cache_update`方法，该方法已从代码中删除
- `cache_save`方法现在直接接受Python对象（字典、列表等），无需手动JSON序列化
- `cache_load`方法直接返回Python对象或None，无需手动JSON反序列化

### 📝 文档更新
- 重构缓存管理部分文档，与实际代码实现保持一致
- 添加了更完整的缓存使用示例和最佳实践



## [0.9.0-alpha.5] - 2025-06-16

### ✨ 功能新增
- exchange配置新增参数multi_ip, 支持交易所rest弹性ip轮询请求
- trader新增graceful_shutdown方法，支持优雅关闭进程
- ws api支持16条ws链接

### 📝 文档更新
- 优化traderv2 api示例及result说明



## [0.9.0-alpha.4] - 2025-06-12

### ✨ 功能新增
- 文档增加get_deposit_address 接口说明
- 文档增加withdrawal 接口说明
- 现启动open quant需要secret_id和secret_key认证，请在config.toml中配置（目前仅测试网，点击 右上角头像-访问秘钥-新建秘钥 获得SecretId和SecretKey）
  ```toml
  [web]
  is_production = false
  secret_id = "xxx"
  secret_key = "xxxx"
  ```

### 💥 不兼容改动
- nb8sdk初始化不再需要传is_production参数，会直接使用config.toml中的web.is_production参数



## [0.9.0-alpha.3] - 2025-06-06

### 💥 不兼容改动
- 修改最大持仓返回结构体包含名义价值和数量
  ```python
  {
      "long_notional": 1000.0,
      "short_notional": 1000.0,
      "long_quantity": 0.001,
      "short_quantity": 0.001
  }
  ```
  - 最大可开仓数量数据字典，包含:
    - `long_notional`: 浮点数，多仓对应Quote名义价值
    - `short_notional`: 浮点数，空仓对应Quote名义价值
    - `long_quantity`: 浮点数，多仓对应Base数量
    - `short_quantity`: 浮点数，空仓对应Base数量

### 🐛 问题修复
- [BitgetSwap] 可以通过extra传入new_cid字段来设置新的cid，注意此方法会返回传入的订单的id，因为交易所返回的id是空,并且经查询，订单id没变
- [BitgetSwap] 修复websocket重连资费重复推送的问题



## [0.9.0-alpha.2] - 2025-06-04

### 🐛 问题修复
- 修复req id异常导致ws api下单没响应的问题
- get_open_order_by_id、cancel_order支持通过id/cid 并完善相关文档及示例
- 修正http_request文档
- 修复bitget host处理
- 修复gateSwap只订阅orderAndFill时不推送订单的问题
- 修复bitgetSwap OrderAndFill推送的order symbol错误问题

### 🔧 优化改进
- 优化http_request的请求body，body改成dict直接传入
- bitgetSwap批量撤单 没有订单时不再报错



## [0.9.0-alpha.1] - 2025-05-30

### 🐛 问题修复
- 修复部分交易所order and fill异常情况
- 修复获取稳定币余额没做0.0001最小值处理问题
- 修复transfer、sub transfer、get funding fee 文档问题
- 修复BinanceMargin的get balances返回的asset不正确问题
- 修复get kline 接口
- gate swap获取最大持仓返回币的数量
- 修复ws api下单精度未做处理的问题



## [0.9.0] - 2025-05-20

### 💥 不兼容改动
- 移除所有回调函数中的context字段
- 正式发布traderV2版本，可通过config.toml中设置`trader_version = "V2"`启用（默认为V2），若需使用旧版请设置`trader_version = "V1"`
- v2版本命令结构扁平化，不再使用多层嵌套，例如下单命令格式：(extra字段为可选字段，用于兼容各交易所特殊字段)
  ```json
  {
    "method": "PlaceOrder",
    "account_id": 1,
    "order": {
        "cid": "test1234567",
        "symbol": "BTC_USDT",
        "order_type": "Limit",
        "side": "Buy",
        "pos_side": "Long",
        "price": 50000.0,
        "amount": 0.1,
        "time_in_force": "GTC"
    },
    "params": {
        "is_dual_side": false,
        "leverage": 10,
        "margin_mode": "Cross",
        "market_order_mode": "Normal"
    },
    "extra": {
        "奇葩字段": "奇葩值"
    }
  }
  ```

### ✨ 功能新增
- traderV2支持直接调用交易所API，无需使用publish命令，默认同步执行，可通过`sync=false`参数设置为异步，可通过generate=True来生成命令去publish
- 所有接口支持通过extra参数设置动态参数，兼容各交易所特殊字段
- 新增OrderAndFill频道订阅（通过on_order_and_fill回调接收），同时订阅订单频道和用户私有成交频道，优先推送较快的数据（一般私有成交速度更快）。私有成交数据仅包含成交数量和均价，可能缺少订单类型、方向等字段，可通过order结构体的source字段区分数据来源。该频道可与订单频道并行订阅，互不干扰
- bitget支持通过WebSocket API撤单
- 交易系统自动请求并缓存交易所instruments数据，可在config.toml中通过`instruments_refresh_sec = 300`配置刷新间隔
- 当交易所Instrument变化时会触发相应回调：on_instrument_added（新增）、on_instrument_removed（删除）和on_instrument_updated（更新）
- 下单时自动处理价格和数量，包括价格乘数、数量乘数、步长、精度、最小交易量和最小名义价值处理
- 安全市价单简化配置，无需传入price_tick和price_precision，可通过OrderParams中的market_order_slippage参数自定义滑点（默认0.002）
- 支持交易所BitmartSwap、BitmartSpot、ZoomexSwap、ZoomexSpot、WhitebitSwap、WhiteSpot、DeribitSwap、DeribitSpot、DeribitFuture（交割合约）、DeribitOption（期权）



## [0.8.7] - 2025-06-04
### 🐛 问题修复
- 修复mexcspot的bbo订阅频道为10ms的
- kucoin main 分支 兼容了 "交易所必填字段返回null"
- [GateSwap] 最大持仓价值转换为数量, 获取最大持仓修复值过大问题
- [BinanceSwap] 最大持仓价值转换为数量



## [0.8.6] - 2025-05-28

### ✨ 功能新增
- 交易所配置支持host、ws_host、ws_api_host 自定义配置，此host优先于colo和testnet

### 🐛 问题修复
- [HuobiSwap] 增加get_all_open_orders 接口
- [BitgetSwap] 修复获取最大持仓部分杠杆值报错的问题
- [KucoinSwap] get_fungding_rate增加周期
- 修复get balance小币种为空时返回错误值的情况



## [0.8.5] - 2025-05-20

### 💥 不兼容改动
- on_latency 回调request_id字段修改为account id

### ✨ 功能新增
- 支持IsDualSideExt、SetDualSidePositionExt、AmendOrderExt、CancelOrderExt同步命令 带Ext后缀的会有新的命令格式，可以动态自定义参数，兼容部分交易所奇葩字段

### 🐛 问题修复
- [BinanceMargin] 修正深度行情推送的exchange字段
- [Binance] 获取instruments开启压缩
- [Phemex] 修复多余日志输出、订单推送、最大持仓问题
- [HuobiSwap] min qty改为默认1 代表最少一张合约
- [BitgetMargin] 获取当前借币的数额加上利息
- [BitgetSpot] 修正现货的ws order推送的市价买单的数量为计价币数量
- [CoinW] 修复instrument和下单bug



## [0.8.4] - 2025-04-30

### 💥 不兼容改动
- 安全市价单必须传price tick和price_precision，slippage默认是0.002  {"market_order_mode":{"Safe":{"price_precision":5,"price_tick":0.00001,"slippage":0.002}}}

### 🐛 问题修复
- [OkxSwap] 资金费用结算去重
- [BitgetSwap] 资金费用结算去重
- [HuobiSpot] 修复余额错误, min notional使用10
- [HuobiSwap] min qty使用contrat size
- ws api正常下安全市价单
- 修复nb8_sdk 错误日志上传问题



## [0.8.3] - 2025-04-27

### 🐛 问题修复
- [BybitSwap] rest查询资金费率增加费率周期
- [HuobiSpot] 修复自定义host报错问题
- [nb8_sdk] 缓存存储到工作目录，增加stats结构体文档说明



## [0.8.2] - 2025-04-24

### 🐛 问题修复
- [HuobiSwap] cid生成去除0
- [BinanceSpot] 修复1000系列交易对行情数据没处理的问题
- [BitgetSwap] 修复amount tick用错字段的问题
- 修复on_funding_fee没有触发问题
- 修复python 的sys path导入问题，现在能正确将当前工作目录，和strategy.py父目录加入到sys.path中

### ✨ 功能新增
- [BybitSwap] 支持资金费结算的ws订阅
- [KucoinMargin] 支持高频接口
- [KucoinSpot] 支持高频接口

### 🔧 优化改进
- 优化示例代码，现在仅保留strategy.py，其余文件移除



##  [0.8.1] - 2025-04-22

### 💥 不兼容改动
- base_strategy.py新增on_funding_fee回调

### ✨ 功能新增
- 文档新增实盘强停状态变更说明 具体参考文件misc/trader_api_doc.md 5.7 账户强停功能
- 新增FundingFee、MarkPrice同步命令

### 🐛 问题修复
- [BitgetSwap] 修复ws position资金费为空的情况
- [BinanceSwap] 修复以下问题:
  - 现货平均成交均价未考虑prefix_multiplier。
  - 无法单独订阅funding_fee。
  - position order 未按订阅symbol过滤。



## [0.8.0] - 2025-04-18


### 💥 不兼容改动
- trader_py改名为trader


### ✨ 功能新增

- 支持rust nb8sdk 使用方法参考文件trader_api_doc.md
- websocket支持订阅FundingFee，rest支持获取FundingFee
- 支持HuobiSpot
- rest支持根据symbol获取标记价格及获取全部交易对标记价格
- trader支持request方法

### 🔧 优化改进

- GateSwap、GateSpot默认不再使用ws api下单，如果需要使用ws api下单，请在config.toml中将exchanges.use_ws_api设置为true
- 错误处理系统重构
  - 错误码分类
    - 通用错误 (4001-4009)
      - 基础错误 (4001)
      - 请求频率限制 (4002)
      - Cookie过期 (4003)
      - 余额不足 (4004)
      - API密钥或IP无效 (4005)
      - 网络错误 (4006)
      - 精度错误 (4007)
      - 保证金类型无需更改 (4008)
      - 重试请求 (4009)
    - 订单相关错误 (4101-4103)
      - FOK订单被拒绝 (4101)
      - 订单未找到 (4102)
      - Maker单立即成交错误 (4103)
    - API相关错误 (5001-5002)
      - 接口未实现 (5001)
      - 网络连接错误 (5002)



## [0.7.18] - 2025-04-07


### 🔧 优化改进
- 优化nb8_sdk， 优化可用余额接口及前端展示

### 🐛 问题修复
- [BitgetSpot] 修复get bbo报错问题
- [KucoinSpot] 修复min_notional错误问题
- [HuobiSwap] 全部接口验证及bug修复
- 深度推送问题修复



## [0.7.17] - 2025-04-01


### ✨ 功能新增
- 支持自定义设置交易所用不用ws api接口， 通过config.toml中的exchanges.use_ws_api字段设置
  - use_ws_api: 使用websocket api去下单，默认 BinanceSpot、OkxSwap、OkxSpot、OkxMargin、GateSwap、GateSpot为true, BinanceSwap为false 其余暂不支持
- 新增nb8_sdk, 具体使用方法参考nb8_sdk/README.md 以及nb8_sdk/example.py
- 支持coinw

### 🔧 优化改进
- 当使用trader publish命令的时候释放gil锁，避免其他回调阻塞

### 🐛 问题修复
- [CoinexSwap] 余额为0时请求返回报错
- [Bybit] 运行过程中bybit报错 程序停止问题
- [Bybit] 有持仓返回positions为0
- [Huobi] 火币行情timestamp是16位，改成13位
- [Huobi] 火币订阅一档行情，推出来多档，改成订阅几档就是几档



## [0.7.16] - 2025-03-21

### ✨ 功能新增
- 支持ApexSwap,DydxSwap,PhemexSwap,HyperLiquidSwap,HyperLiquidSpot
- 支持市场数据源处理模式选择：可通过config.toml中的data_source.market_mode字段设置,想用旧版本方式的话全部设置为"ALL"
  - `Latest`：只处理最新数据，新数据会顶掉未处理的数据，减少延迟。
  - `All`：全部数据顺序处理，适用于不希望丢失数据的情况。
- `data_source.market_mode` 配置项：
  ```toml
  [data_source.market_mode]
  mark_price = "Latest"
  bbo = "Latest"
  depth = "Latest"
  funding = "All"
  trade = "All"

### 🔧 优化改进
- biannce合约默认改成使用rest下单（更快）

### 🐛 问题修复
- 修复coinex资金费率ws和rest不一致问题
- 修复kucoin合约的乘数问题
- 修复gate ws api下单解析结果错误问题
- 修复binance合约增量深度报错问题
- 修复bybit获取balance的可用余额为0.0001问题



## [0.7.15] - 2025-03-14

### ✨ 功能新增
- GateSwap GateSpot支持ws api下单

### 🔧 优化改进
- bitget、biannce兼容1M前缀的symbol的处理

### 🐛 问题修复
- 修复binance ws api改post only单时的问题



## [0.7.14] - 2025-03-13

### ✨ 功能新增
- 同步命令支持BalanceByCoin 查询指定币种的余额

### 🔧 优化改进
- 当start、subscribe函数调用完才会去调用python其他的回调函数
- 优化bn资金费率，rest查询资金费率结算周期不再为None

### 🐛 问题修复
- 修复binance现货ws api下post only单报错、根据cid撤单报错问题
- 修复MexcSpot下单报错



## [0.7.13] - 2025-03-07

### ✨ 功能新增
- 支持MexcSpot

### 🐛 问题修复
- 修复binance合约资金费率问题



## [0.7.12] - 2025-03-07

### 🐛 问题修复
- 修复binance现货和合约同时配置时可能下单错误问题

### 🔧 优化改进
- 去除kraken postOnly单的多余报错
- 改进Coinex网络错误处理和响应状态检查



## [0.7.11] - 2025-03-05

### ✨ 功能新增
- 现异步指令走websocket api, 包含PlaceOrder、BatchPlaceOrder、AmendOrder、CancelOrder、BatchCancelOrder、BatchCancelOrderById 目前支持的交易所有BinanceSwap、BinanceSpot、OkxSwap、OkxSpot、OkxMargin
- 新增缓存（加载、更新、保存）方法 使用方法参考策略示例中的load_cache_data等方法

### 🔧 优化改进
- 图表优化，图表示例优化


## [0.7.10] - 2025-02-25

### 💥 不兼容改动
- on_order_canceled 回调增加修改订单时传入的order id和symbol
- on_order_amended 回调增加修改订单时传入的order

### 🐛 问题修复
- 修复binance现货的maker单错误
- 修复gate ws订单解析报错问题 注意：gate现货市价买ws推送的amount和filled都是成交额（名义价值）

### ✨ 功能新增
- 支持dex交易所Apex

## [0.7.9] - 2025-02-21

### 💥 不兼容改动
- 图表组结构修改，支持多个图表组

### 🐛 问题修复
- 修复gate swap的bbo错误
- bn现货行情增加时间戳



## [0.7.8] - 2025-02-21

### 💥 不兼容改动
- 可通过OrderParams的market_order_mode字段设置市价单模式，Safe/Normal,
    默认Safe: 安全市价单模式下碰到的市价单会转化为ioc加滑点的限价单，需要传递价格和价格步长 滑点默认0.2%可配置("market_order_mode":{"Safe": {"price_tick": 0.001,"slippage": 0.002}})
    Normal: 普通市价单不用传价格不做限制 ({"market_order_mode":"Normal"})

### 🔧 优化改进
- 下单时可以不传订单id
- 优化bitget margin的get_borrow_limit接口

### 🐛 问题修复
- 修复bitget下单响应order_id为null的问题 当为null时置空



## [0.7.7] - 2025-02-19

### 💥 不兼容改动
- 日志修改默认为不限频，实盘运行时通过config.toml中的log.rate_limit字段设置限频
- 修改on_fundings为on_funding
- 修改on_instruments为on_instrument
- 修改on_positions为on_position
- 将on_position_update迁移到on_position
- 修改on_balances为on_balance
- 去除on_balance

### 🐛 问题修复
- 修复huobi资金费率获取到的下次资金费率时间为0的问题
- kraken depth增加timestamp时间戳， checksum计算失败后重置depth缓存
- gate现货订单已成交修正减去fee
- coinex订单推送的pos side默认值改为None

### ✨ 功能新增
- 新增图表功能
- bitget margin增加get_order_by_id方法
- 新增交易所deepcoin、coinw、bitfinex、phemex
- dex同步命令支持根据key来接受命令响应
- Rest订阅新增Instrument



## [0.7.6] - 2025-02-11

### 🐛 问题修复
- 安全市价单模式下碰到的部分交易所下单问题

## [0.7.5] - 2025-02-11

### ✨ 功能新增
- bitget支持划转
- 支持在order params加leverage_order_mode来设置杠杆下单时的自动借款、还款(http://api.ymengkj.cn/docs/guide/latest/api-commands/#placeorder)
- 支持日志限频（令牌桶算法）
- 支持通过OrderParams的market_order_mode字段设置市价单模式，Safe/Normal, 默认Safe: 市价单必须传价格如果名义价值超过20000u则修正为20000u，Normal: 普通市价单不用传价格不做限制

### 🐛 问题修复
- bitget杠杆获取balance
- 修复binance对于1000系列交易对适配问题

### 🔧 优化改进
- 优化http请求网络延迟
- 优化客户端id生成（create_cid方法）速度到300ns/次



## [0.7.4] - 2025-01-27

### 💥 不兼容改动
- 将on funding和on instrument分别合并到on fundings和on instruments



## [0.7.3] - 2025-01-24

### 🐛 问题修复
- 修复balance的余额不为0时查出来可能为0.0001的问题



## [0.7.2] - 2025-01-23

### ✨ 功能新增
- 支持bitget现货杠杆



## [0.7.1] - 2025-01-20

### 🐛 问题修复
- 修复bybit下单报错、下单返回的order_id错误问题

### 🔧 优化改进
- 支持dex建立两条socket，一条用于读取，一条用于发送命令
- 优化trader.log方法的web参数为true，默认发送给web

### ✨ 功能新增
- 支持实盘列表页数据发送


## [0.7.0] - 2025-01-17

### 🐛 问题修复
- 修复同步命令可能碰到的网络错误问题



## [0.6.10] - 2025-01-15


### 💥 不兼容改动
- 去除config.toml中set_cpu配置

### 🐛 问题修复
- 修复设置线程优先级时由于权限问题导致程序panic问题,现在只会打印警告信息

### 🔧 优化改进
- 优化发给web的button结构




## [0.6.7] - 2025-01-14


### 🐛 问题修复
- 修复bybit,okx 交易所已知问题

### ✨ 功能新增
- 自动检测dex sdk进程状态，当进程挂掉/心跳超时 自动重启dex sdk并发送订阅消息
- 优化读取python策略文件和配置文件错误信息



## [0.6.6] - 2025-01-10

### 💥 不兼容改动
- 移除python的sys.path 增加策略文件(strategy.py)所在目录， 改为用户在strategy.py中自定义添加sys.path

### 🐛 问题修复
- 修复策略文件(strategy.py)中导入其他模块失败问题导致运行时报错



## [0.6.5] - 2025-01-09

### ✨ 新特性
- 支持策略配置文件热更新，当配置文件更新后，会自动读取配置文件内容并调用方法on_config_update
- python的sys.path 增加策略文件所在目录
- base_strategy.py 无需放在 strategy.py 同级目录下，只需在strategy.py中根据路径导入即可

### 🐛 问题修复
- 修复okx margin、bybit一些已知问题
- 修复dex的同命令超时事件未配置成功问题



## [0.6.4] - 2025-01-08

### 💥 不兼容改动
- BaseStrategy 独立为单独的 python 模块文件(base_strategy.py), 和strategy.py同级
- 修改配置文件加载方式，直接再rust中加载策略配置文件 然后传给python策略

### ✨ 新特性
- 修改 Python 模块加载机制，支持托管者通过ws传递策略配置
- 无需再安装pip包，trader已经内部集成
- python策略新增on_stop回调，策略停止时会调用
- trader 新增 batch_publish 方法，支持批量发布命令

### 🐛 问题修复
- 修复 kraken 订单更新中的filled字段不正确问题
- 优化 kraken nonce生成为ns时间戳
- 修复bitget ws老是重连问题

### 🔧 优化改进
- 重构代码结构，提升可维护性
- 优化错误处理机制
- 改进日志输出格式
- 完善单元测试覆盖率

### 📝 文档更新
- 添加代码注释和文档说明
- 更新配置文件示例
- 补充 Python 模块开发指南

### 🔨 依赖更新
- 更新 trader 依赖到最新版本
- 更新 quant_common 依赖到最新版本

### 📦 其他改动
- 优化 CI/CD 流程
- 改进错误提示信息
- 代码格式化和清理

### 🚀 性能优化
- 优化 Python 模块加载性能
- 改进配置文件解析效率

## 如何升级

1. 删除原来strategy.py中的BaseStrategy类
2. 将base_strategy.py复制到strategy.py同级目录下
2. 确保配置文件使用正确的 TOML 格式
3. 删除之前安装的trader pip包
4. 替换最新open_quant
