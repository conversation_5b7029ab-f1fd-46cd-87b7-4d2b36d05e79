[package]
edition = "2024"
name = "open_quant"
version = "0.9.0-alpha.13"

[workspace]
members = ["."]

[dependencies]
algo_common = { git = "ssh://***************:8022/openquant/algo_common.git", branch = "main" }
quant_common = { git = "ssh://***************:8022/newquant/quant_common.git", branch = "dev" }
trader = { git = "ssh://***************:8022/quant/trader.git", branch = "dev" }
# trader = { path = "../trader" }
# algo_common = {path = "../algo_common"}

tokio = { version = "1.44.0", default-features = false, features = [
    "net",
    "io-util",
    "macros",
    "fs",
    "rt-multi-thread",
] }
pyo3 = { version = "0.25", features = [
    "auto-initialize",
    "macros",
    "abi3-py310",
] }
pythonize = "0.25"
serde = "1.0"
serde_json = "1.0"
serde_path_to_error = "0.1.16"
serde_plain = "1.0"
tracing = "0.1"
once_cell = "1.20"
async-trait = "0.1"
sonic-rs = "0.3"
toml = "0.8"
reqwest = { version = "0.12", default-features = false, features = [
    "rustls-tls",
    "json",
    "socks",
] }
time = { version = "0.3.41", features = ["parsing"] }
chrono = "0.4"
base64 = "0.22"
openssl-sys = { version = "0.9", features = ["vendored"] }
futures = "0.3.31"
dashmap = "6.1"
sha2 = "0.10.8"
sysinfo = "0.35"
fs2 = "0.4"

[build-dependencies]
chrono = "0.4.38"

[profile.release]
opt-level = 3
lto = "fat"
codegen-units = 1
strip = true
panic = "abort"

[patch."ssh://***************:8022/exchanges/binance.git"]
binance = { path = "../binance" }
