# 定义阶段
stages:
  - build-linux
  - generate-changelog
  - create-release-package
  - github-release

build-linux:
  stage: build-linux
  tags:
    - shell
  rules:
    - if: $CI_PIPELINE_SOURCE == "trigger" && $CI_COMMIT_BRANCH == "dev"
      when: always
    - if: $CI_PIPELINE_SOURCE == "push" && $CI_COMMIT_BRANCH == "dev"
      when: always
  cache:
    key: github-cli
    paths:
      - $HOME/.local/bin/gh
  before_script:
    # 清理之前的构建文件
    - |
      if [ -d "target" ]; then
        sudo chown -R gitlab-runner:gitlab-runner target/
        sudo chmod -R u+w target/
      fi
  script:
    - echo "Starting build open_quant for Linux"
    - cargo update
    - CROSS_NO_WARNINGS=0 cross build --release --target x86_64-unknown-linux-gnu
  after_script:
    # 确保构建后的文件权限正确
    - sudo chown -R gitlab-runner:gitlab-runner .
  artifacts:
    paths:
      - target/x86_64-unknown-linux-gnu/release/open_quant
    expire_in: 1 week

# # Windows 构建任务 (合并 open_quant 和 wheel)
# build-windows:
#   stage: build
#   tags:
#     - shell
#   variables:
#     RUSTFLAGS: "-C relocation-model=dynamic-no-pic"
#     PYTHON_CONFIGURE_OPTS: "--enable-shared"
#     PYO3_CROSS_PYTHON_VERSION: "3.10"
#     PYO3_CROSS_LIB_DIR: "/usr/x86_64-w64-mingw32/lib"
#   script:
#     # 构建 open_quant
#     - echo "Starting build open_quant for Windows..."
#     - cargo build --release --target x86_64-pc-windows-gnu

#   artifacts:
#     paths:
#       - target/x86_64-pc-windows-gnu/release/open_quant.exe
#     expire_in: 1 week

# 生成 Changelog
generate-changelog:
  stage: generate-changelog
  tags:
    - shell
  script:
    - |
      echo "Generating CHANGELOG.md..."
  artifacts:
    paths:
      - CHANGELOG.md
    expire_in: 1 week

# 打包任务
create-release-package:
  stage: create-release-package
  tags:
    - shell
  needs:
    - build-linux
    # - build-windows
    - generate-changelog
  script:
    - VERSION=$(grep '^version = ' Cargo.toml | cut -d '"' -f2)
    - mkdir -p release_package
    # 复制配置文件
    - rsync -av misc/config.toml release_package/
    - rsync -av misc/base_strategy.py release_package/
    # 复制示例
    - rsync -avz --exclude="__pycache__" misc/examples release_package/
    - rsync -avz misc/docs release_package/
    # 复制构建产物
    - cp target/x86_64-unknown-linux-gnu/release/open_quant release_package/
    # - cp target/x86_64-pc-windows-gnu/release/open_quant.exe release_package/
    - cp CHANGELOG.md release_package/
    # 创建 zip 包
    - cd release_package
    - tar -cvzf "../open_quant_${VERSION}.tar.gz" ./*
  artifacts:
    paths:
      - open_quant_*.tar.gz
    expire_in: 1 week

# # 发布任务
# github-release:
#   stage: github-release
#   tags:
#     - shell
#   cache:
#     key: github-cli
#     paths:
#       - $HOME/.local/bin/gh
#   before_script:
#     - export PATH=$HOME/.local/bin:$PATH  # 确保 gh 在 PATH 中
#   rules:
#     - if: $CI_COMMIT_TAG
#   variables:
#     GH_TOKEN: $GITHUB_TOKEN
#     GH_REPO: "open-quant-hub/OpenQuant"   # 替换为你的仓库名
#     CI_SERVER_URL: "https://git.nb8.net/"
#   script:
#     - VERSION=${CI_COMMIT_TAG#v}

#     # 检查必要的环境变量
#     - |
#       if [ -z "$GITHUB_TOKEN" ]; then
#         echo "Error: GITHUB_TOKEN is not set"
#         exit 1
#       fi

#     # 只在 gh 命令不存在时安装
#     - |
#       if ! command -v gh &> /dev/null; then
#         echo "Installing GitHub CLI..."
#         mkdir -p $HOME/.local/bin
#         curl -L https://github.com/cli/cli/releases/latest/download/gh_$(curl -s https://api.github.com/repos/cli/cli/releases/latest | grep -o '"tag_name": ".*"' | cut -d'"' -f4 | cut -c2-)_linux_amd64.tar.gz | tar xz
#         mv gh_*/bin/gh $HOME/.local/bin/
#         export PATH=$HOME/.local/bin:$PATH
#       fi

#     # 下载和处理构建产物
#     - |
#       # # 下载并解压 artifacts.zip
#       # curl --location --output artifacts.zip \
#       #   "${CI_SERVER_URL}/api/v4/projects/${CI_PROJECT_ID}/jobs/artifacts/dev/download?job=create-release-package" \
#       #   --header "JOB-TOKEN: ${CI_JOB_TOKEN}"

#       # echo "Listing current directory contents:"
#       # ls -la

#       echo "Listing current directory contents:"
#       ls -la

#       # 检查所需文件是否已存在
#       if [ ! -f "open_quant_${VERSION}.tar.gz" ] || [ ! -f "CHANGELOG.md" ]; then
#         echo "Required files not found, downloading and extracting artifacts..."
#         curl --location --output artifacts.zip \
#           "${CI_SERVER_URL}/api/v4/projects/${CI_PROJECT_ID}/jobs/artifacts/dev/download?job=create-release-package" \
#           --header "JOB-TOKEN: ${CI_JOB_TOKEN}"

#         echo "Unzipping artifacts.zip..."
#         unzip artifacts.zip
#       else
#         echo "Required files already exist, skipping download and extraction"
#       fi

#       # 检查 tar 文件
#       echo "Checking tar file:"
#       file "open_quant_${VERSION}.tar.gz"

#       # 使用 tar 命令而不是 tar xzf(去掉 z 选项)
#       echo "Extracting tar file..."
#       tar xf "open_quant_${VERSION}.tar.gz"

#       echo "Listing extracted contents:"
#       ls -la

#       if [ ! -f "CHANGELOG.md" ]; then
#         echo "Error: CHANGELOG.md not found"
#         exit 1
#       fi

#     # 提取最新版本的变更记录
#     - |
#       echo "Extracting latest version changes from CHANGELOG.md..."
#       awk '/^## \[.*\]/{if(p)exit; p=1}p' CHANGELOG.md > LATEST_CHANGES.md

#       if [ ! -s LATEST_CHANGES.md ]; then
#         echo "Error: Failed to extract latest changes"
#         exit 1
#       fi

#       echo "Latest changes:"
#       cat LATEST_CHANGES.md

#     # 使用 GitHub CLI 创建 release
#     - |
#       # 设置环境变量
#       export GITHUB_TOKEN="${GH_TOKEN}"

#       # 验证认证状态
#       if ! gh auth status; then
#         echo "Failed to authenticate with GitHub"
#         exit 1
#       fi

#       # 创建 release
#       gh release create "${CI_COMMIT_TAG}" \
#         --repo "${GH_REPO}" \
#         --title "Release ${CI_COMMIT_TAG}" \
#         --notes-file LATEST_CHANGES.md \
#         "open_quant_${VERSION}.tar.gz"
