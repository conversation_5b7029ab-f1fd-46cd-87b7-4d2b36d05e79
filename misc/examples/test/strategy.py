"""
strategy.py

测试策略模块 - 继承自BaseStrategy
只在start函数中查询accountid为0的余额
"""
import copy
import json
import time
import traderv2 # type: ignore
import base_strategy
import requests

class Strategy(base_strategy.BaseStrategy):
    def __init__(self, cex_configs, dex_configs, config, trader: traderv2.TraderV2):
        """
        初始化策略对象。

        Args:
            cex_configs (list): CEX 交易所配置列表。
            dex_configs (list): DEX 交易所配置列表。
            config (dict): 策略配置。
            trader (trader.Trader): Rust Trader 对象。
        """
        self.cex_configs = cex_configs
        self.has_account = len(cex_configs) > 0
        self.dex_configs = dex_configs
        self.trader = trader
        self.config = config
        self.symbol = "XRP_USDT"

        self.last_bbo_trigger = 0
        self.pending_cid = ""
        self.pending_order_time = {}

        # 默认配置
        if not config:
            self.config = {
                "send_to_web": False,
            }

    def name(self):
        """
        返回策略的名称。

        Returns:
            str: 策略名称。
        """
        return "测试策略"

    def start(self):
        """
        策略启动函数 - 仅查询accountid为0的余额
        """
        try:
            self.trader.log("启动测试策略")

            position= self.trader.get_position(0, self.symbol)['Ok']
            self.trader.log(f"持仓: {position}")

            is_dual_side = self.trader.is_dual_side(0)['Ok']

            result = self.trader.post_stop_order(
                    account_id=0,
                    symbol=self.symbol,
                    pos_side="Long",
                    take_profit={
                        "trigger_price": {"MarkPrice": 3.0},  # 止盈触发价
                        "trigger_action": {
                            "quantity": 2,                     # 部分止盈
                            "execute_type": "Market",
                            "typ": "Normal"
                        }
                    },
                    stop_loss={
                        "trigger_price": {"ContractPrice": 2.0},  # 止损触发价
                        "trigger_action": {
                            "execute_type": "Market",             # 市价止损
                            "typ": "Normal"
                        }
                    },
                    is_dual_side=is_dual_side
                )
            result = json.dumps(result, indent=2, ensure_ascii=False)
            self.trader.log(f"止盈止损订单: {result}")

            # 查询止盈止损订单
            result = self.trader.get_stop_orders(0, self.symbol)
            result_js = json.dumps(result, indent=2, ensure_ascii=False)
            self.trader.log(f"止盈止损订单: {result_js}")

            # 过滤当前挂单
            open_stop_orders = [order for order in result['Ok'] if order['status'] == 'Open']
            self.trader.log(f"当前挂单: {open_stop_orders}")

            # 找到止盈单
            take_profit_order = [order for order in open_stop_orders if order['take_profit']]
            order_id = take_profit_order[0]['take_profit']['id']

            # 修改止盈止损订单
            result = self.trader.amend_stop_order(
                account_id=0,
                symbol=self.symbol,
                id=order_id,
                take_profit={
                    "trigger_price": {"MarkPrice": 3.1},     # 指数价格触发
                    "trigger_action": {
                        "quantity": 3,
                        "execute_type": "Market",
                        "typ": "Normal"
                    }
                },
            )
            self.trader.log(f"修改订单结果: {result}")

            # 获取修改后的订单
            result = self.trader.get_stop_orders(0, self.symbol, id = order_id)
            result_js = json.dumps(result, indent=2, ensure_ascii=False)
            self.trader.log(f"修改后的订单: {result_js}")

            # 撤单
            for order in open_stop_orders:
                id = order['stop_loss']['id'] if order['stop_loss'] else order['take_profit']['id']
                res = self.trader.cancel_stop_order(0, self.symbol, id = id)
                if 'Ok' in res:
                    self.trader.log(f"撤单成功: {id}")
                else:
                    self.trader.log(f"撤单失败: {id}")

            # query_params = {"instId": "BTC-USD-SWAP"}
            # result = self.trader.request(
            #     account_id=0,
            #     method="GET",
            #     path="/api/v5/market/ticker",
            #     auth=False,  # 公开接口不需要认证
            #     query=query_params,
            #     body=None,
            #     url=None,
            #     headers=None,
            #     generate=False
            # )

            # if "Ok" in result:
            #     ticker_data = result["Ok"]
            #     print(f"BTC-USD-SWAP行情数据: {ticker_data}")
            # else:
            #     error = result.get("Err", "未知错误")
            #     print(f"获取OKX行情失败: {error}")

            # # ✅ GET请求示例 - 获取交易所合约信息
            # url = "https://api.gateio.ws/api/v4/futures/usdt/contracts/BTC_USDT"
            # result = self.trader.http_request(url, "GET", None, None)

            # if "Ok" in result:
            #     contract_info = result["Ok"]
            #     print(f"BTC_USDT合约信息: {contract_info}")
            # else:
            #     error = result.get("Err", "未知错误")
            #     print(f"获取合约信息失败: {error}")

            # # ✅ POST请求示例 - 使用JSONPlaceholder测试API
            # post_url = "https://jsonplaceholder.typicode.com/posts"
            # post_data = {
            #     "title": "测试POST请求",
            #     "body": "这是一个测试POST请求的内容",
            #     "userId": 1
            # }
            # headers = {"Content-Type": "application/json"}

            # # 直接传递Python字典，Rust端会自动序列化为JSON
            # result = self.trader.http_request(post_url, "POST", post_data, headers)
            # if "Ok" in result:
            #     post_response = result["Ok"]
            #     print(f"POST请求结果: {post_response}")
            #     print(f"创建的文章ID: {post_response.get('id')}")
            # else:
            #     error = result.get("Err", "未知错误")
            #     print(f"POST请求失败: {error}")

            # balances = self.trader.get_balances(0)
            # self.trader.log(f"账户ID {0} 的余额信息: {balances}")

            # # 查询accountid为0的余额
            # if self.has_account:
            #     account_id = 0
            #     cmd =     {
            #         "account_id": 0,
            #         "method": "UsdtBalance",
            #         "sync": True
            #     }

            #     result = self.trader.publish(cmd)

            #     # 记录余额信息到日志
            #     self.trader.log(f"账户ID {account_id} 的余额信息: {result}")
            # else:
            #     self.trader.log("没有配置账户信息，无法查询余额")

            # order = {
            #     "cid": traderv2.create_cid(self.cex_configs[0]["exchange"]),
            #     "symbol": self.symbol,
            #     "order_type": "Market",
            #     "side": "Buy",
            #     "pos_side": "Long",
            #     "price": 100.,
            #     "amount": 0.781,
            #     "time_in_force": "GTC"
            # }
            # params = {
            #     "is_dual_side": False,
            #     "market_order_mode": "Normal"
            # }

            # res = self.trader.place_order(0, order, params, sync=True)
            # self.trader.log(f"下单结果: {res}")

            # orders = []
            # for i in range(5):
            #     order_copy = copy.deepcopy(order)
            #     order_copy['cid'] = traderv2.create_cid(self.cex_configs[0]["exchange"])
            #     orders.append(order_copy)
            # self.trader.log(f"批量下单订单: {orders}")
            # res = self.trader.batch_place_order(0, orders, params, sync=False)
            # self.trader.log(f"批量下单结果: {res}")

            # # 撤单
            # res = self.trader.cancel_order(0, res["Ok"], self.symbol)
            # self.trader.log(f"撤单结果: {res}")
        except Exception as e:
            self.trader.log(f"测试策略启动错误: {str(e)}")

    def subscribes(self):
        """
        返回策略订阅的事件列表。
        测试策略不需要订阅任何数据

        Returns:
            list: 空列表，表示不订阅任何数据
        """
        return [
            {
                "account_id": 0,
                "sub": {
                    "SubscribeWs": [
                        {
                            "OrderAndFill": [self.symbol]
                        },
                        # {
                        #     "Order": [self.symbol]
                        # },
                        # {
                        #     "Bbo": [self.symbol]
                        # },
                        # {
                        #     "FundingFee": [self.symbol]
                        # },
                        # {
                        #     "Kline": {
                        #         "symbols": [self.symbol],
                        #         "interval": "1m"
                        #     }
                        # }
                    ]
                }
            },
            {
                "account_id": 1,
                "sub": {
                    "SubscribeWs": [
                        # {
                        #     "OrderAndFill": [self.symbol]
                        # },
                        # {
                        #     "Order": [self.symbol]
                        # },
                        {
                            "Bbo": [self.symbol]
                        },
                        # {
                        #     "FundingFee": [self.symbol]
                        # },
                        # {
                        #     "Kline": {
                        #         "symbols": [self.symbol],
                        #         "interval": "1m"
                        #     }
                        # }
                    ]
                }
            },
            # {
            #     "sub": {
            #         "SubscribeTimer": {
            #             "update_interval": {
            #                 "secs": 1,
            #                 "nanos": 0
            #             },
            #             "name": "post_order"  # 每10秒检查一次订单状态
            #         }
            #     }
            # },
            # {
            #     "account_id": 0,
            #     "sub": {
            #         "SubscribeWs": [
            #             {
            #                 "Bbo": ["BTC_USDT"]
            #             }
            #         ]
            #     }
            # },
        ]

    def on_timer_subscribe(self, timer_name):
        """
        处理定时器事件

        根据不同的定时器名称执行相应的定时任务

        参数:
            timer_name: 定时器名称
        """
        self.trader.log(f"定时器事件: {timer_name}")

        cid = traderv2.create_cid(self.cex_configs[0]["exchange"])
        if self.pending_cid != "":
            self.trader.log(f"pending_cid: {self.pending_cid}", level="WARN")
        self.pending_cid = cid
        order = {
            "cid": cid,
            "symbol": self.symbol,
            "order_type": "Limit",
            "side": "Buy",
            "pos_side": "Long",
            "price": 64931.4,
            "amount": 0.0007,
            "time_in_force": "Limit"
        }
        params = {
            "is_dual_side": False,
            # "market_order_mode": "Normal"
        }

        cmd = self.trader.place_order(0, order, params, sync=False, generate=True)['Ok']
        self.pending_order_time[cid] = time.time() * 1000
        return {
            'cmds': [cmd]
        }

    def on_order_submitted(self, account_id, order_id_result, order):
        """
        订单提交成功时触发的方法。

        Args:
            account_id (str): 账户 ID。

            order_result (str): 包含订单 ID 的 Result 可能为 Err。
            order (dict): 订单信息。
        """
        # id = order_id_result.get("Ok")
        cid = order.get("cid")
        if cid in self.pending_order_time:
            # 计算延迟
            delay = time.time() * 1000 - self.pending_order_time[cid]
            if delay > 500:
                self.trader.log(f"{cid}订单提交延迟: {delay:.2f}ms", level="WARN")
            del self.pending_order_time[cid]
        self.pending_cid = ""
        self.trader.log(f"订单提交结果: {order_id_result}， {order}")
        if id:
            # 撤单
            cmd = self.trader.cancel_order(0, self.symbol, order_id=None, cid=cid, sync=False, generate=True)['Ok']

            cmds = [cmd]
            return {
                'cmds': cmds
            }

    def on_order_canceled(self, account_id, result, id, symbol):
        """
        订单取消成功时触发的方法。

        Args:
            account_id (str): 账户 ID。
            order_id (str): 订单 ID。
            id (str): 撤单时传入的订单id/订单cid。
            symbol(str): 撤单时传入的symbol。
        """
        self.trader.log(f"订单取消结果: {result}, {id}, {symbol}")

    def on_funding_fee(self, account_id, context, funding_fee):
        """
        资金费结算时触发的方法。
        """
        self.trader.log(f"资金费结算: {funding_fee}")

    def on_stop(self):
        """
        策略停止时的回调函数
        """
        self.trader.log("测试策略已停止")

    def on_order_and_fill(self, account_id, order):
        """
        订单/用户私有成交更新时触发的方法, 订单频道和用户成交频道哪个快推哪个。

        Args:
            account_id (str): 账户 ID。
            order: 订单对象。
        """
        self.trader.log(f"订单/用户私有成交更新: {order}", level="INFO", color="blue")

    def on_order(self, account_id, order):
        """
        订单回调函数
        """
        now = time.time() * 1000
        self.trader.log(f"订单回调: {order} {now}")

    def on_bbo(self, exchange, bbo):
        """
        买卖盘回调函数
        """
        pass
        # cid = traderv2.create_cid(self.cex_configs[0]["exchange"])
        # if self.pending_cid != "":
        #     self.trader.log(f"pending_cid: {self.pending_cid}", level="WARN")
        # self.pending_cid = cid
        # order = {
        #     "cid": cid,
        #     "symbol": self.symbol,
        #     "order_type": "Limit",
        #     "side": "Buy",
        #     "pos_side": "Long",
        #     "price": 64931.4,
        #     "amount": 0.0007,
        #     "time_in_force": "Limit"
        # }
        # params = {
        #     "is_dual_side": False,
        #     # "market_order_mode": "Normal"
        # }

        # # cmd = self.trader.place_order(0, order, params, sync=False, generate=True)['Ok']
        # self.pending_order_time[cid] = time.time() * 1000
        # return {
        #     'cmds': [{'account_id': 0, 'method': 'PlaceOrder', 'order': {'symbol': 'BTC_USDT', 'order_type': 'Limit', 'side': 'Buy', 'price': 107291.991, 'amount': 0.003618410869409628, 'time_in_force': 'Limit', 'cid': 'BinanceSwap429705378', 'pos_side': None}, 'params': {'is_dual_side': False, 'margin_mode': 'Cross', 'leverage': 50}}, {'account_id': 0, 'method': 'PlaceOrder', 'order': {'symbol': 'BTC_USDT', 'order_type': 'Limit', 'side': 'Sell', 'price': 106224.3095, 'amount': 0.****************, 'time_in_force': 'Limit', 'cid': 'BinanceSwap774484793', 'pos_side': None}, 'params': {'is_dual_side': False, 'margin_mode': 'Cross', 'leverage': 50}}]
        # }

    def on_position(self, account_id, positions):
        """
        持仓回调函数
        """
        self.trader.log(f"持仓回调: {positions}")

    def on_kline(self, exchange, kline):
        """
        行情K线更新时触发的方法。

        Args:
            exchange (str): 交易所名称。
            kline: 行情K线对象。
        """
        self.trader.log(f"行情K线更新: {kline}")
